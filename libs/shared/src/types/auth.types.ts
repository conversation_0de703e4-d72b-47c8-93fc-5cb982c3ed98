// Authentication request types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Authentication response types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginResponse {
  user: User;
  tokens: AuthTokens;
}

export interface RegisterResponse {
  user: User;
  tokens: AuthTokens;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  isVerified: boolean;
  role?: {
    code: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Token validation
export interface TokenValidationResponse {
  valid: boolean;
  user?: User;
  expiresAt?: string;
}

// Decoded JWT token
export interface DecodedToken {
  id?: string | number;
  farmer_id?: string;
  exp?: number;
  name?: string;
  email?: string;
  mobile_number?: string;
  role?: {
    code: string;
    name: string;
  };
}
