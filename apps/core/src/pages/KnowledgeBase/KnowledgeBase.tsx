import React, { useState } from 'react'

// Mock articles/guides data
const knowledgeArticles = [
  {
    id: 1,
    title: 'Sustainable Crop Rotation Practices',
    category: 'Soil Management',
    summary: 'Learn effective crop rotation strategies to enhance soil health and maximize yields while reducing pest and disease pressure.',
    content: `
      Crop rotation is a critical practice in sustainable agriculture that helps maintain soil fertility,
      reduce pest and disease pressure, and improve overall farm productivity. This guide explores the
      principles of effective crop rotation and provides practical strategies for implementation.
      
      ## Key Benefits of Crop Rotation
      
      - Improves soil structure and fertility
      - Breaks pest and disease cycles
      - Reduces weed pressure
      - Enhances biodiversity
      - Optimizes nutrient use
      
      ## Rotation Planning Guidelines
      
      When designing your crop rotation plan, consider these key factors:
      
      1. **Plant families**: Rotate crops from different botanical families
      2. **Nutrient needs**: Alternate between heavy feeders and soil builders
      3. **Root depth**: Vary shallow and deep-rooted crops
      4. **Cover crops**: Incorporate nitrogen-fixing cover crops
      5. **Seasonal considerations**: Plan for climate-appropriate timing
      
      ## Sample 4-Year Rotation Plan
      
      | Year 1 | Year 2 | Year 3 | Year 4 |
      |--------|--------|--------|--------|
      | Legumes (beans, peas) | Leafy greens & brassicas | Fruiting crops (tomatoes, peppers) | Root crops (carrots, onions) |
      
      This rotation helps maximize soil health by alternating between crops with different nutrient needs and growth habits.
    `,
    author: 'Dr. Maria Rodriguez',
    date: '2023-03-15',
    readTime: '8 min',
    tags: ['soil health', 'crop rotation', 'sustainable farming'],
    imageUrl: 'https://placehold.co/600x400'
  },
  {
    id: 2,
    title: 'Integrated Pest Management for Corn',
    category: 'Pest Management',
    summary: 'Comprehensive guide to managing corn pests using integrated approaches that minimize chemical inputs.',
    content: `
      Integrated Pest Management (IPM) offers a balanced approach to pest control that aims to minimize risks
      to human health, beneficial organisms, and the environment. This guide focuses specifically on
      IPM strategies for corn production.
      
      ## Principles of IPM in Corn
      
      - Regular field scouting and monitoring
      - Accurate pest identification
      - Understanding pest life cycles
      - Establishing economic thresholds for intervention
      - Using multiple control tactics
      
      ## Common Corn Pests and Management Strategies
      
      ### European Corn Borer
      **Prevention**: Select resistant varieties, adjust planting dates
      **Biological Control**: Trichogramma wasps, Bt formulations
      **Chemical Control**: Only when thresholds are exceeded
      
      ### Corn Rootworm
      **Prevention**: Crop rotation, tolerant varieties
      **Cultural Control**: Altered planting dates, trap crops
      **Chemical Control**: Targeted soil insecticides when necessary
      
      ### Corn Earworm
      **Monitoring**: Pheromone traps, regular inspection
      **Biological Control**: Predatory insects, Bt sprays
      **Chemical Control**: Timed applications based on silk emergence
      
      ## Record Keeping and Evaluation
      
      Maintain detailed records of:
      - Pest observations
      - Weather conditions
      - Control measures used
      - Effectiveness of interventions
      
      Use this information to refine your approach each season.
    `,
    author: 'James Chen, Ph.D.',
    date: '2023-05-22',
    readTime: '12 min',
    tags: ['pest management', 'corn', 'IPM', 'sustainable farming'],
    imageUrl: 'https://placehold.co/600x400'
  },
  {
    id: 3,
    title: 'Understanding Soil Testing Results',
    category: 'Soil Management',
    summary: 'How to interpret soil test reports and make informed decisions about fertilization and amendments.',
    content: `
      Soil testing is essential for making informed decisions about soil amendments and fertilization.
      This guide helps farmers understand their soil test results and develop effective soil management plans.
      
      ## Key Soil Test Parameters
      
      ### pH Level
      - **Acidic**: pH 5.0-6.5
      - **Neutral**: pH 6.5-7.5
      - **Alkaline**: pH 7.5-8.5
      
      Most crops prefer a slightly acidic to neutral pH. Adjustments can be made using lime (to raise pH)
      or sulfur (to lower pH).
      
      ### Macronutrients
      
      **Nitrogen (N)**
      - Essential for leaf and stem growth
      - Mobile in soil, can leach easily
      - Recommendations typically based on crop needs rather than test results
      
      **Phosphorus (P)**
      - Critical for root development and flowering
      - Relatively immobile in soil
      - Levels below 20 ppm typically require supplementation
      
      **Potassium (K)**
      - Important for overall plant health and disease resistance
      - Medium mobility in soil
      - Optimal levels range from 150-250 ppm
      
      ### Secondary Nutrients and Micronutrients
      
      Pay attention to calcium, magnesium, sulfur, and micronutrients like zinc, iron, and boron,
      which can be limiting factors for specific crops.
      
      ## Interpreting Recommendations
      
      Soil test recommendations typically include:
      - Lime or acidifying amendments needed
      - NPK fertilizer recommendations
      - Secondary nutrient additions
      - Organic matter considerations
      
      Always calibrate recommendations to your specific crops, yield goals, and farming system.
    `,
    author: 'Dr. Emily Williams',
    date: '2023-01-30',
    readTime: '10 min',
    tags: ['soil health', 'soil testing', 'fertilization'],
    imageUrl: 'https://placehold.co/600x400'
  },
  {
    id: 4,
    title: 'Water Conservation Techniques in Agriculture',
    category: 'Water Management',
    summary: 'Practical methods to improve water efficiency on farms while maintaining crop yields.',
    content: `
      Water is becoming an increasingly precious resource for agriculture. This guide outlines
      practical water conservation techniques that can help farmers reduce water usage while
      maintaining or improving crop yields.
      
      ## Irrigation Efficiency
      
      ### Drip Irrigation
      - 90-95% efficiency rating
      - Delivers water directly to root zone
      - Reduces evaporation and runoff
      - Best for row crops, orchards, and vineyards
      
      ### Microsprinklers
      - 80-90% efficiency rating
      - Covers larger area than drip
      - Lower filtration requirements
      - Good for tree crops and some field applications
      
      ### Precision Sprinklers
      - Variable rate technology
      - Weather-responsive controllers
      - Soil moisture sensor integration
      - Can reduce water use by 30-50%
      
      ## Soil Management for Water Retention
      
      - **Cover crops**: Increase organic matter and improve water infiltration
      - **No-till practices**: Maintain soil structure and reduce evaporation
      - **Mulching**: Reduces soil temperature and evaporation rates
      - **Compost application**: Improves water-holding capacity
      
      ## Water Monitoring and Scheduling
      
      - **Soil moisture sensors**: Place at multiple depths in root zone
      - **Evapotranspiration (ET) based scheduling**: Adjust irrigation based on crop needs and weather
      - **Deficit irrigation**: Strategic under-irrigation during drought-tolerant growth stages
      
      ## Rainwater Harvesting
      
      - Farm ponds and reservoirs
      - Contour bunding and terracing
      - Swales and retention basins
      - Rooftop collection systems for buildings
      
      Implementing these techniques can lead to significant water savings while maintaining farm productivity and profitability.
    `,
    author: 'Robert Johnson',
    date: '2023-04-10',
    readTime: '9 min',
    tags: ['water conservation', 'irrigation', 'sustainable farming'],
    imageUrl: 'https://placehold.co/600x400'
  },
  {
    id: 5,
    title: 'Climate-Smart Agriculture Practices',
    category: 'Sustainability',
    summary: 'Adapting farming methods to climate change while reducing greenhouse gas emissions.',
    content: `
      Climate-smart agriculture (CSA) addresses the challenges of climate change through three main pillars:
      sustainably increasing productivity, enhancing resilience to climate change, and reducing greenhouse gas
      emissions where possible. This guide explores practical CSA approaches for farmers.
      
      ## Adaptive Farming Techniques
      
      ### Diversification Strategies
      - Multiple crop varieties with different climate tolerances
      - Mixed farming systems (crops + livestock)
      - Agroforestry and silvopasture systems
      - Enterprise diversification to spread risk
      
      ### Resilient Crop Selection
      - Drought-tolerant varieties
      - Heat-resistant cultivars
      - Short-season varieties for variable growing seasons
      - Traditional varieties adapted to local conditions
      
      ## Carbon Sequestration Practices
      
      - **Cover cropping**: Keep living roots in soil year-round
      - **Reduced tillage**: Minimize soil disturbance
      - **Compost application**: Build soil organic matter
      - **Agroforestry**: Integrate trees into farming systems
      - **Managed grazing**: Optimize carbon capture in pastures
      
      ## Resource Efficiency
      
      - Precision agriculture technologies
      - Renewable energy integration (solar pumps, bioenergy)
      - Improved nitrogen management to reduce N₂O emissions
      - Water conservation and efficiency measures
      
      ## Climate Risk Management
      
      - Weather forecasting tools and early warning systems
      - Crop insurance and risk sharing mechanisms
      - Flexible planting calendars
      - Diversified market strategies
      
      Implementing these climate-smart practices can help farmers build resilience, reduce their carbon
      footprint, and potentially access emerging carbon markets and sustainability premiums.
    `,
    author: 'Dr. Sarah Kim',
    date: '2023-02-08',
    readTime: '11 min',
    tags: ['climate change', 'sustainability', 'adaptation', 'carbon sequestration'],
    imageUrl: 'https://placehold.co/600x400'
  },
  {
    id: 6,
    title: 'Organic Certification Process Explained',
    category: 'Certification',
    summary: 'Step-by-step guide to obtaining organic certification for your farm, including requirements and preparation tips.',
    content: `
      Organic certification can open new markets and premium prices for your agricultural products.
      This guide walks you through the certification process and helps you prepare for a successful application.
      
      ## Certification Steps
      
      ### 1. Transition Period
      - Most land requires 36 months without prohibited substances
      - Develop and implement an organic system plan
      - Begin recordkeeping systems
      - Start using approved inputs only
      
      ### 2. Select a Certifier
      - USDA-accredited certifying agents
      - Consider location, fees, and expertise with your production type
      - Request application packets from potential certifiers
      
      ### 3. Submit Application
      Your application package typically includes:
      - Detailed farm history and current setup
      - Field maps and facility diagrams
      - List of all substances used in the last 3+ years
      - Organic System Plan (OSP)
      - Fee payment
      
      ### 4. Inspection
      - Physical inspection of all production areas
      - Review of recordkeeping systems
      - Tracing of product flow (audit trail)
      - Assessment of contamination risks
      
      ### 5. Review and Certification Decision
      - Certifier reviews inspection report
      - May request additional information
      - Issues certification or notices of non-compliance
      
      ## Ongoing Requirements
      
      - Annual renewal and inspection
      - Updated Organic System Plan as changes occur
      - Continuous recordkeeping
      - Notification of major changes
      
      ## Common Challenges
      
      - **Recordkeeping**: Create systems for seed purchases, input applications, harvests, sales
      - **Buffer zones**: Establish adequate separation from conventional operations
      - **Input verification**: Ensure all materials are allowed under organic standards
      - **Pest management**: Develop prevention-based strategies
      
      Start the certification process well in advance of when you want to sell organic products,
      as the process typically takes 3-6 months from application to final certification.
    `,
    author: 'Thomas Miller',
    date: '2023-06-05',
    readTime: '14 min',
    tags: ['organic farming', 'certification', 'regulations'],
    imageUrl: 'https://placehold.co/600x400'
  }
]

export default function KnowledgeBase() {
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedArticle, setSelectedArticle] = useState(null)
  
  // Available categories derived from articles
  const categories = ['All Categories', ...new Set(knowledgeArticles.map(article => article.category))]
  
  // Filter articles based on category and search query
  const filteredArticles = knowledgeArticles.filter(article => {
    const categoryMatch = selectedCategory === 'All Categories' || article.category === selectedCategory
    const searchMatch = 
      searchQuery === '' || 
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return categoryMatch && searchMatch
  })
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Agricultural Knowledge Base</h1>
      <p className="text-lg mb-6">Access expert guides and resources for modern farming practices</p>
      
      {selectedArticle ? (
        // Article detail view
        <div>
          <button 
            onClick={() => setSelectedArticle(null)}
            className="mb-6 flex items-center text-green-600 hover:text-green-700"
          >
            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
            Back to Articles
          </button>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="h-64 bg-gray-200 relative">
              <img 
                src={selectedArticle.imageUrl} 
                alt={selectedArticle.title} 
                className="w-full h-full object-cover"
              />
            </div>
            
            <div className="p-6">
              <div className="flex flex-wrap items-center mb-4">
                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mr-2">
                  {selectedArticle.category}
                </span>
                <span className="text-gray-500 text-sm">{selectedArticle.date}</span>
                <span className="text-gray-500 text-sm mx-2">•</span>
                <span className="text-gray-500 text-sm">{selectedArticle.readTime} read</span>
              </div>
              
              <h1 className="text-2xl font-bold mb-4">{selectedArticle.title}</h1>
              <p className="text-gray-600 italic mb-6">{selectedArticle.summary}</p>
              
              <div className="prose max-w-none">
                {selectedArticle.content.split('\n').map((paragraph, idx) => (
                  <p key={idx} className="mb-4">{paragraph}</p>
                ))}
              </div>
              
              <div className="mt-8 pt-4 border-t">
                <p className="text-sm text-gray-500">Written by {selectedArticle.author}</p>
                <div className="mt-2 flex flex-wrap">
                  {selectedArticle.tags.map(tag => (
                    <span key={tag} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded mr-2 mb-2">
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Articles list view
        <>
          {/* Search and Filter Section */}
          <div className="bg-white p-6 rounded-lg shadow-md mb-8">
            <div className="flex flex-col md:flex-row md:items-end gap-4">
              <div className="flex-1">
                <label className="block text-gray-700 mb-2">Search Articles</label>
                <input
                  type="text"
                  placeholder="Search by title, content or tags..."
                  className="w-full p-2 border rounded"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="w-full md:w-48">
                <label className="block text-gray-700 mb-2">Filter by Category</label>
                <select 
                  className="w-full p-2 border rounded"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
              
              <button 
                onClick={() => {
                  setSearchQuery('')
                  setSelectedCategory('All Categories')
                }}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200"
              >
                Reset Filters
              </button>
            </div>
          </div>
          
          {/* Results Count */}
          <div className="mb-6">
            <p className="text-gray-600">Showing {filteredArticles.length} articles</p>
          </div>
          
          {/* Articles Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredArticles.map(article => (
              <div 
                key={article.id} 
                className="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setSelectedArticle(article)}
              >
                <div className="h-40 bg-gray-200 relative">
                  <img 
                    src={article.imageUrl} 
                    alt={article.title} 
                    className="w-full h-full object-cover"
                  />
                  <span className="absolute top-2 right-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                    {article.category}
                  </span>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">{article.title}</h3>
                  <p className="text-gray-600 text-sm line-clamp-2 mb-3">{article.summary}</p>
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>{article.date}</span>
                    <span>{article.readTime} read</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Empty state */}
          {filteredArticles.length === 0 && (
            <div className="bg-white p-8 rounded-lg shadow-md text-center">
              <h3 className="text-xl font-semibold mb-2">No articles found</h3>
              <p className="text-gray-600 mb-4">Try adjusting your search or filter settings.</p>
              <button 
                onClick={() => {
                  setSearchQuery('')
                  setSelectedCategory('All Categories')
                }}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                View All Articles
              </button>
            </div>
          )}
        </>
      )}
    </div>
  )
}
