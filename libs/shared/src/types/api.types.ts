// Generic API types
export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface ApiEndpoints {
  auth: {
    login: string;
    register: string;
    logout: string;
    refreshToken: string;
    forgotPassword: string;
    resetPassword: string;
    changePassword: string;
    validate: string;
  };
  products: {
    list: string;
    detail: string;
    search: string;
    categories: string;
    trending: string;
    similar: string;
  };
  profile: {
    get: string;
    create: string;
    update: string;
    delete: string;
    addresses: string;
    preferences: string;
  };
  orders: {
    list: string;
    create: string;
    detail: string;
    update: string;
    cancel: string;
    track: string;
  };
  cart: {
    get: string;
    add: string;
    update: string;
    remove: string;
    clear: string;
  };
}

// HTTP methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Request configuration
export interface RequestConfig {
  method: HttpMethod;
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
}

// Service URLs configuration
export interface ServiceUrls {
  auth: string;
  product: string;
  profile: string;
  order: string;
  cart: string;
}
