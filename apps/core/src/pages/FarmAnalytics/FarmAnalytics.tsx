import React, { useState } from 'react'

export default function FarmAnalytics() {
  const [timeRange, setTimeRange] = useState('year')
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Farm Analytics Dashboard</h1>
        <div className="flex items-center space-x-2">
          <span>Time Range:</span>
          <select 
            className="p-2 border rounded"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
            <option value="year">Last Year</option>
            <option value="custom">Custom</option>
          </select>
        </div>
      </div>
      
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-500 font-medium mb-2">Total Yield</h3>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-green-600">27.4</span>
            <span className="ml-1 text-sm text-gray-500">tons</span>
          </div>
          <p className="mt-2 text-sm text-green-600">↑ 12% from previous period</p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-500 font-medium mb-2">Revenue</h3>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-green-600">$48,250</span>
          </div>
          <p className="mt-2 text-sm text-green-600">↑ 8% from previous period</p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-500 font-medium mb-2">Costs</h3>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-red-500">$29,120</span>
          </div>
          <p className="mt-2 text-sm text-red-500">↑ 4% from previous period</p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-500 font-medium mb-2">Profit Margin</h3>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-blue-600">39.6%</span>
          </div>
          <p className="mt-2 text-sm text-blue-600">↑ 3.2% from previous period</p>
        </div>
      </div>
      
      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Yield Chart */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Crop Yield Trends</h2>
          <div className="h-64 bg-gray-100 flex items-center justify-center rounded">
            {/* Placeholder for actual chart */}
            <div className="text-center">
              <div className="flex items-end h-48 space-x-4 justify-center">
                <div className="w-8 bg-green-300 rounded-t" style={{ height: '60%' }}></div>
                <div className="w-8 bg-green-400 rounded-t" style={{ height: '75%' }}></div>
                <div className="w-8 bg-green-500 rounded-t" style={{ height: '82%' }}></div>
                <div className="w-8 bg-green-600 rounded-t" style={{ height: '90%' }}></div>
                <div className="w-8 bg-green-700 rounded-t" style={{ height: '95%' }}></div>
              </div>
              <div className="flex justify-between mt-2 text-xs text-gray-500">
                <span>Jan</span>
                <span>Mar</span>
                <span>Jun</span>
                <span>Sep</span>
                <span>Dec</span>
              </div>
              <p className="mt-4 text-gray-500">Chart visualization would be implemented with a charting library</p>
            </div>
          </div>
        </div>
        
        {/* Revenue vs Costs */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Revenue vs Costs</h2>
          <div className="h-64 bg-gray-100 flex items-center justify-center rounded">
            {/* Placeholder for actual chart */}
            <div className="text-center">
              <div className="flex items-end h-48 space-x-8 justify-center">
                <div className="flex space-x-1">
                  <div className="w-8 bg-green-500 rounded-t" style={{ height: '65%' }}></div>
                  <div className="w-8 bg-red-400 rounded-t" style={{ height: '40%' }}></div>
                </div>
                <div className="flex space-x-1">
                  <div className="w-8 bg-green-500 rounded-t" style={{ height: '70%' }}></div>
                  <div className="w-8 bg-red-400 rounded-t" style={{ height: '45%' }}></div>
                </div>
                <div className="flex space-x-1">
                  <div className="w-8 bg-green-500 rounded-t" style={{ height: '85%' }}></div>
                  <div className="w-8 bg-red-400 rounded-t" style={{ height: '50%' }}></div>
                </div>
                <div className="flex space-x-1">
                  <div className="w-8 bg-green-500 rounded-t" style={{ height: '90%' }}></div>
                  <div className="w-8 bg-red-400 rounded-t" style={{ height: '55%' }}></div>
                </div>
              </div>
              <div className="flex justify-between mt-2 text-xs text-gray-500">
                <span>Q1</span>
                <span>Q2</span>
                <span>Q3</span>
                <span>Q4</span>
              </div>
              <div className="flex justify-center space-x-6 mt-4">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                  <span className="text-xs text-gray-500">Revenue</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-400 rounded-full mr-1"></div>
                  <span className="text-xs text-gray-500">Costs</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Weather Impact and Soil Health */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Weather Impact Analysis</h2>
          <div className="flex mb-4">
            <div className="w-1/2 border-r pr-4">
              <h3 className="text-gray-500 font-medium mb-2">Current Conditions</h3>
              <div className="flex items-center">
                <span className="text-4xl mr-2">🌤️</span>
                <div>
                  <p className="font-medium">Partly Cloudy</p>
                  <p className="text-gray-500">72°F / 22°C</p>
                </div>
              </div>
              <div className="mt-2">
                <p className="text-sm">Precipitation: <span className="font-medium">20%</span></p>
                <p className="text-sm">Humidity: <span className="font-medium">65%</span></p>
                <p className="text-sm">Wind: <span className="font-medium">8 mph NW</span></p>
              </div>
            </div>
            <div className="w-1/2 pl-4">
              <h3 className="text-gray-500 font-medium mb-2">Impact Assessment</h3>
              <div className="mb-2">
                <p className="text-sm font-medium">Wheat</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: '80%' }}></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">Optimal growing conditions</p>
              </div>
              <div className="mb-2">
                <p className="text-sm font-medium">Corn</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">Needs more precipitation</p>
              </div>
              <div>
                <p className="text-sm font-medium">Soybeans</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">Good conditions</p>
              </div>
            </div>
          </div>
          <div className="border-t pt-3">
            <h3 className="text-gray-500 font-medium mb-2">7-Day Forecast Impact</h3>
            <p className="text-sm">Rising temperatures expected to accelerate corn growth. Consider increasing irrigation schedule for optimal yield.</p>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Soil Health Tracking</h2>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <h3 className="text-gray-500 font-medium mb-2">Nitrogen (N)</h3>
              <div className="flex items-center mb-1">
                <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '70%' }}></div>
                </div>
                <span className="text-sm">70%</span>
              </div>
              <p className="text-xs text-gray-500">Adequate levels</p>
            </div>
            <div>
              <h3 className="text-gray-500 font-medium mb-2">Phosphorus (P)</h3>
              <div className="flex items-center mb-1">
                <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-red-500 h-2 rounded-full" style={{ width: '45%' }}></div>
                </div>
                <span className="text-sm">45%</span>
              </div>
              <p className="text-xs text-gray-500">Low - supplementation advised</p>
            </div>
            <div>
              <h3 className="text-gray-500 font-medium mb-2">Potassium (K)</h3>
              <div className="flex items-center mb-1">
                <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
                <span className="text-sm">85%</span>
              </div>
              <p className="text-xs text-gray-500">Optimal levels</p>
            </div>
            <div>
              <h3 className="text-gray-500 font-medium mb-2">pH Level</h3>
              <div className="flex items-center mb-1">
                <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                </div>
                <span className="text-sm">6.2</span>
              </div>
              <p className="text-xs text-gray-500">Slightly acidic - monitor</p>
            </div>
          </div>
          <div className="border-t pt-3">
            <h3 className="text-gray-500 font-medium mb-2">Recommendations</h3>
            <ul className="text-sm list-disc list-inside">
              <li>Apply phosphate fertilizer to improve phosphorus levels</li>
              <li>Consider lime application to gradually raise pH</li>
              <li>Schedule soil testing in 30 days to monitor improvements</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Market Trends */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Agricultural Market Trends</h2>
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-4 py-2 text-left">Crop</th>
                <th className="px-4 py-2 text-left">Current Price</th>
                <th className="px-4 py-2 text-left">Change (7d)</th>
                <th className="px-4 py-2 text-left">Forecast (30d)</th>
                <th className="px-4 py-2 text-left">Market Demand</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="px-4 py-3">Wheat</td>
                <td className="px-4 py-3">$7.25/bushel</td>
                <td className="px-4 py-3 text-green-600">↑ 3.2%</td>
                <td className="px-4 py-3">Upward trend</td>
                <td className="px-4 py-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '80%' }}></div>
                  </div>
                </td>
              </tr>
              <tr className="border-b">
                <td className="px-4 py-3">Corn</td>
                <td className="px-4 py-3">$5.80/bushel</td>
                <td className="px-4 py-3 text-red-600">↓ 1.8%</td>
                <td className="px-4 py-3">Stable</td>
                <td className="px-4 py-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '65%' }}></div>
                  </div>
                </td>
              </tr>
              <tr className="border-b">
                <td className="px-4 py-3">Soybeans</td>
                <td className="px-4 py-3">$13.40/bushel</td>
                <td className="px-4 py-3 text-green-600">↑ 2.5%</td>
                <td className="px-4 py-3">Upward trend</td>
                <td className="px-4 py-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </td>
              </tr>
              <tr className="border-b">
                <td className="px-4 py-3">Cotton</td>
                <td className="px-4 py-3">$0.85/pound</td>
                <td className="px-4 py-3 text-red-600">↓ 0.5%</td>
                <td className="px-4 py-3">Slight downward</td>
                <td className="px-4 py-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                </td>
              </tr>
              <tr>
                <td className="px-4 py-3">Rice</td>
                <td className="px-4 py-3">$16.20/cwt</td>
                <td className="px-4 py-3 text-green-600">↑ 1.2%</td>
                <td className="px-4 py-3">Stable</td>
                <td className="px-4 py-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className="mt-4 text-sm text-gray-500">
          <p>Market data updated daily. Source: Agricultural Market Information System (AMIS)</p>
        </div>
      </div>
      
      {/* Farm Performance Calculator */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Farm Performance Analysis</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-gray-500 font-medium mb-3">Resource Utilization</h3>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between border-b pb-1">
                <span>Water Usage</span>
                <span>2500 Liters/acre</span>
              </div>
              <div className="flex justify-between border-b pb-1">
                <span>Seeds & Planting</span>
                <span>25 kg/acre</span>
              </div>
              <div className="flex justify-between border-b pb-1">
                <span>Fertilizers & Nutrients</span>
                <span>150 kg/acre</span>
              </div>
              <div className="flex justify-between border-b pb-1">
                <span>Labor Hours</span>
                <span>45 hrs/acre</span>
              </div>
              <div className="flex justify-between border-b pb-1">
                <span>Equipment Usage</span>
                <span>20 hrs/acre</span>
              </div>
              <div className="flex justify-between font-medium">
                <span>Sustainability Score</span>
                <span className="text-[#22C55E]">85%</span>
              </div>
            </div>
            <button className="w-full bg-[#F97316] text-white py-2 rounded hover:bg-[#F97316]/90 transition duration-200">
              Optimize Resources
            </button>
          </div>
          
          <div>
            <h3 className="text-gray-500 font-medium mb-3">Harvest Projections</h3>
            <div className="space-y-3">
              <div className="bg-gray-50 p-3 rounded">
                <h4 className="font-medium mb-1">Expected Yield</h4>
                <p className="text-2xl font-bold text-[#22C55E]">4.8 tons/acre</p>
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>Based on current conditions</span>
                  <span>+12%</span>
                </div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <h4 className="font-medium mb-1">Crop Health</h4>
                <p className="text-2xl font-bold text-[#22C55E]">92%</p>
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>Overall crop vitality</span>
                  <span>+8%</span>
                </div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <h4 className="font-medium mb-1">Growth Rate</h4>
                <p className="text-2xl font-bold text-[#F97316]">1.2 cm/day</p>
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>Average daily growth</span>
                  <span>+2.2%</span>
                </div>
              </div>
            </div>
            <button className="w-full mt-4 border border-[#22C55E] text-[#22C55E] py-2 rounded hover:bg-[#22C55E]/10 transition duration-200">
              Download Report
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
