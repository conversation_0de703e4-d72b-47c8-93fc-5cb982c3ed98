{"installation": {"version": "21.0.3"}, "$schema": "./node_modules/nx/schemas/nx-schema.json", "nxCloudId": "68208bf34242b71292fe1ae3", "plugins": [{"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}], "generators": {"@nx/react": {"application": {"babel": true, "style": "tailwind", "linter": "none", "bundler": "vite"}, "component": {"style": "tailwind"}, "library": {"style": "tailwind", "linter": "none", "unitTestRunner": "none"}}}}