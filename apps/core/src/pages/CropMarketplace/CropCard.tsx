import React from 'react'
import { Link } from 'react-router-dom'
import { Product } from '@shared'

interface CropCardProps {
  product: Product
}

export const CropCard: React.FC<CropCardProps> = ({ product }) => {
  const {
    id,
    name,
    category,
    pricing,
    images,
    availability,
    certifications,
    rating,
    reviewCount,
    location,
    specifications
  } = product;

  // Get stock status from availability
  const getStockStatus = () => {
    if (!availability.inStock) return 'Out of Stock';
    if (availability.quantity < 10) return 'Low Stock';
    return 'In Stock';
  };

  const stockStatus = getStockStatus();
  const imageUrl = images?.[0] || "https://via.placeholder.com/300x200";
  const price = pricing.basePrice;
  const certification = certifications?.[0] || 'Standard';
  const plotSize = specifications?.size || '100 sq.m.';
  // Certification badge
  const certificationClassName = 
    "absolute top-2 right-2 text-xs px-3 py-1 rounded-full text-white " + 
    (certification === 'Organic' 
      ? "bg-[#6f0aa1]" 
      : certification === 'Non-GMO' 
        ? "bg-[#6f0aa1]" 
        : "bg-blue-700");
  
  // Stock status badge
  const stockClassName = 
    "text-xs px-2 py-1 rounded-full " + 
    (stockStatus === 'In Stock' 
      ? "bg-[#6f0aa1] text-white" 
      : stockStatus === 'Low Stock' 
        ? "bg-yellow-500 text-white" 
        : "bg-red-500 text-white");

  return (
    <div className="bg-[#1a1625] rounded-lg overflow-hidden shadow-md">
      <Link to={`/crops/${id}`} className="block relative">
        <img
          src={imageUrl || "https://via.placeholder.com/300x200"}
          alt={name}
          className="w-full h-48 object-cover"
        />
        <span className={certificationClassName}>
          {certification}
        </span>
      </Link>
      
      <div className="p-4">
        <Link to={`/crops/${id}`} className="block">
          <h3 className="text-lg font-medium text-white mb-1">{name}</h3>
          <div className="flex justify-between items-center mt-1 mb-1">
            <span className="text-gray-400">{category.name}</span>
            <span className="text-gray-400">{location.city}, {location.state}</span>
          </div>

          <div className="mb-3">
            <p className="text-xs text-gray-400">Available: {availability.quantity} {availability.unit}</p>
            {rating > 0 && (
              <p className="text-xs text-gray-400">⭐ {rating.toFixed(1)} ({reviewCount} reviews)</p>
            )}
          </div>

          <div className="flex justify-between items-center mb-4">
            <span className="text-xl font-bold text-white">${price.toFixed(2)}/{pricing.unit}</span>
            <span className={stockClassName}>{stockStatus}</span>
          </div>
        </Link>
      </div>
    </div>
  )
} 