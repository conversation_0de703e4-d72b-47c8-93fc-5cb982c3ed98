import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { CropCard } from './CropCard'
import {
  fetchProducts,
  searchProducts,
  setFilters,
  setSearchQuery,
  fetchCategories,
  fetchStates,
  RootState,
  AppDispatch,
  Product,
  ProductFilters
} from '@libs/shared'

// Define types
type StockStatus = 'In Stock' | 'Low Stock' | 'Out of Stock';

interface CropItem {
  id: string;
  name: string;
  type: string;
  price: number;
  season: string;
  certification: string;
  stockStatus: StockStatus;
  imageUrl: string;
  plotSize: string;
  rating: number;
  reviews: number;
  description: string;
}

// Mock data for crops
const mockCrops: CropItem[] = [
  {
    id: 'crop1',
    name: 'Organic Wheat Seeds',
    type: 'Grain',
    price: 129.99,
    season: 'Spring',
    certification: 'Organic',
    stockStatus: 'In Stock',
    imageUrl: 'https://images.unsplash.com/photo-1535921311544-31a5699c1d36?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
    plotSize: '100 sq.m.',
    rating: 4.8,
    reviews: 124,
    description: 'Premium quality organic wheat seeds ideal for spring planting. High yield, disease-resistant varieties.'
  },
  {
    id: 'crop2',
    name: 'High-Yield Corn Seeds',
    type: 'Grain',
    price: 149.99,
    season: 'Summer',
    certification: 'Non-GMO',
    stockStatus: 'In Stock',
    imageUrl: 'https://images.unsplash.com/photo-1551283279-c919bafc9e3c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
    plotSize: '120 sq.m.',
    rating: 4.7,
    reviews: 89,
    description: 'High-yielding corn seed variety, perfect for summer growing season with excellent drought resistance.'
  },
  {
    id: 'crop3',
    name: 'Premium Tomato Seeds',
    type: 'Vegetable',
    price: 89.99,
    season: 'Spring',
    certification: 'Organic',
    stockStatus: 'Low Stock',
    imageUrl: 'https://images.unsplash.com/photo-1546094096-0df4bcaaa337?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
    plotSize: '80 sq.m.',
    rating: 4.9,
    reviews: 156,
    description: 'Premium heirloom tomato seed selection for vibrant, flavorful produce. Excellent disease resistance.'
  },
  {
    id: 'crop4',
    name: 'Cotton Seeds',
    type: 'Fiber',
    price: 179.99,
    season: 'Summer',
    certification: 'Standard',
    stockStatus: 'In Stock',
    imageUrl: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
    plotSize: '150 sq.m.',
    rating: 4.6,
    reviews: 72,
    description: 'High-quality cotton seeds with excellent fiber yield. Suitable for various soil types.'
  },
  {
    id: 'crop5',
    name: 'Soybean Seeds',
    type: 'Legume',
    price: 119.99,
    season: 'Spring',
    certification: 'Non-GMO',
    stockStatus: 'In Stock',
    imageUrl: 'https://images.unsplash.com/photo-1620421680010-0766ff230392?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
    plotSize: '110 sq.m.',
    rating: 4.5,
    reviews: 94,
    description: 'High-protein soybean varieties for optimal yield. Excellent nitrogen-fixing properties.'
  },
  {
    id: 'crop6',
    name: 'Coffee Plant Seedlings',
    type: 'Permanent',
    price: 249.99,
    season: 'All Year',
    certification: 'Organic',
    stockStatus: 'Low Stock',
    imageUrl: 'https://images.unsplash.com/photo-1610889556528-9a770e32642f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
    plotSize: '200 sq.m.',
    rating: 4.9,
    reviews: 42,
    description: 'Arabica coffee seedlings grown organically. Premium quality for long-term investment.'
  }
];

// Filter options
const cropTypes = ['All Types', 'Grain', 'Vegetable', 'Fiber', 'Legume', 'Permanent'];
const certifications = ['All Certifications', 'Organic', 'Non-GMO', 'Standard'];
const seasons = ['All Seasons', 'Spring', 'Summer', 'Fall', 'Winter', 'All Year'];

// Sort options
const sortOptions = [
  { value: 'featured', label: 'Featured' },
  { value: 'price-asc', label: 'Price: Low to High' },
  { value: 'price-desc', label: 'Price: High to Low' },
  { value: 'rating', label: 'Highest Rated' }
];

export default function CropMarketplace() {
  const [showFilters, setShowFilters] = useState(false);
  const [sortOption, setSortOption] = useState('featured');
  const [currentPage, setCurrentPage] = useState(1);

  const dispatch = useDispatch<AppDispatch>();
  const {
    products,
    searchResults,
    categories,
    states,
    filters,
    searchQuery
  } = useSelector((state: RootState) => state.products);

  // Get current data based on whether we're searching or browsing
  const isSearching = searchQuery.trim().length > 0;
  const currentData = isSearching ? searchResults : products;
  const crops = currentData.data?.data || [];
  const totalPages = currentData.data?.pagination?.totalPages || 1;
  const loading = currentData.isLoading;
  const error = currentData.error;

  // Fetch initial data
  useEffect(() => {
    // Fetch categories and states for filters
    dispatch(fetchCategories());
    dispatch(fetchStates());
  }, [dispatch]);

  // Fetch products when page or filters change
  useEffect(() => {
    if (isSearching) {
      dispatch(searchProducts({
        query: searchQuery,
        page: currentPage,
        limit: 12,
      }));
    } else {
      const productFilters: ProductFilters = {
        page: currentPage,
        limit: 12,
        inStock: true,
        ...filters,
      };
      dispatch(fetchProducts(productFilters));
    }
  }, [dispatch, currentPage, searchQuery, filters, isSearching]);

  // Handle search input changes
  const handleSearchChange = (query: string) => {
    dispatch(setSearchQuery(query));
    setCurrentPage(1); // Reset to first page when searching
  };

  // Apply client-side sorting to the fetched data
  const sortedCrops = React.useMemo(() => {
    if (!crops.length) return [];

    let result = [...crops];

    // Apply sorting
    if (sortOption === 'price-asc') {
      result.sort((a, b) => a.pricing.basePrice - b.pricing.basePrice);
    } else if (sortOption === 'price-desc') {
      result.sort((a, b) => b.pricing.basePrice - a.pricing.basePrice);
    } else if (sortOption === 'rating') {
      result.sort((a, b) => b.rating - a.rating);
    }

    return result;
  }, [crops, sortOption]);

  return (
    <div className="bg-[#0f0a18] min-h-screen">
      {/* Hero section */}
      <div className="bg-[#580080] pt-16 pb-16">
        <div className="container mx-auto px-8">
          <h1 className="text-4xl font-bold text-white mb-4">Premium Crop Plots</h1>
          <p className="text-white text-lg mb-10 max-w-2xl">
            Invest in high-quality crop plots with transparent growing conditions and direct connection to farmers.
          </p>
          
          <div className="flex flex-col md:flex-row gap-4 md:items-center max-w-3xl">
            <div className="relative flex-grow">
              <svg className="absolute top-3 left-3 w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
              </svg>
              <input
                type="text"
                placeholder="Search crops, varieties, or keywords..."
                className="w-full py-2.5 px-4 pl-10 bg-[#1a1625] text-white rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500"
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>
            
            <button 
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center gap-2 px-4 py-2.5 bg-[#6f0aa1] text-white rounded-md focus:outline-none hover:bg-[#7b0bb0]"
            >
              <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M4 21v-7m0-4V3"></path>
                <path d="M12 21v-9m0-4V3"></path>
                <path d="M20 21v-5m0-4V3"></path>
                <path d="M4 14h.01"></path>
                <path d="M12 8h.01"></path>
                <path d="M20 12h.01"></path>
              </svg>
              Filters
            </button>
          </div>
        </div>
      </div>
      
      {/* Main content */}
      <div className="container mx-auto px-8 py-8">
        {/* Results and sorting */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <p className="text-gray-300 mb-4 md:mb-0">
            Showing <span className="font-medium text-white">{sortedCrops.length}</span> results
          </p>
          
          <div className="flex items-center">
            <span className="text-gray-300 mr-2">Sort by:</span>
            <select 
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value)}
              className="bg-[#1a1625] text-white py-1 px-3 border border-[#2a2135] rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
        </div>
        
        {/* Crops grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-[#1a1625] rounded-lg overflow-hidden shadow-md animate-pulse">
                <div className="w-full h-48 bg-gray-700"></div>
                <div className="p-4 space-y-3">
                  <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-700 rounded w-1/2"></div>
                  <div className="h-6 bg-gray-700 rounded w-1/3"></div>
                </div>
              </div>
            ))
          ) : (
            sortedCrops.map(product => (
              <CropCard
                key={product.id}
                product={product}
              />
            ))
          )}
        </div>

        {/* Error state */}
        {error && (
          <div className="text-center py-12">
            <div className="bg-red-900/20 border border-red-500 rounded-lg p-6 max-w-md mx-auto">
              <h3 className="text-xl font-medium text-red-400 mb-2">Error Loading Products</h3>
              <p className="text-red-300 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </div>
        )}

        {/* Empty state */}
        {!loading && !error && sortedCrops.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-white mb-2">No crops found</h3>
            <p className="text-gray-400">Try adjusting your search or filters</p>
          </div>
        )}

        {/* Pagination */}
        {!loading && !error && totalPages > 1 && (
          <div className="flex justify-center items-center gap-4 mt-8">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 bg-[#6f0aa1] text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#7b0bb0]"
            >
              Previous
            </button>
            <span className="text-white">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="px-4 py-2 bg-[#6f0aa1] text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#7b0bb0]"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
