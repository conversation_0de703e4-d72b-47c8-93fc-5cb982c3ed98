import { BaseService } from './base.service';
import {
  UserProfile,
  CreateProfileRequest,
  UpdateProfileRequest,
  UpdateProfilePictureRequest,
  Address,
  CreateAddressRequest,
  UpdateAddressRequest,
  ProfileSearchParams,
  PaginatedResponse,
  UserPreferences,
  UpdatePreferencesRequest,
  ApiResponse,
} from '../types';

export class ProfileService extends BaseService {
  constructor(baseURL: string) {
    super(baseURL);
  }

  // Profile management
  async getMyProfile(): Promise<UserProfile> {
    return this.handleRequest(
      this.api.get<ApiResponse<UserProfile>>('/api/profiles/profile')
    );
  }

  async getProfileById(profileId: string): Promise<UserProfile> {
    return this.handleRequest(
      this.api.get<ApiResponse<UserProfile>>(`/api/profiles/profile/${profileId}`)
    );
  }

  async createProfile(data: CreateProfileRequest): Promise<UserProfile> {
    return this.handleRequest(
      this.api.post<ApiResponse<UserProfile>>('/api/profiles/profile', data)
    );
  }

  async updateProfile(data: UpdateProfileRequest): Promise<UserProfile> {
    return this.handleRequest(
      this.api.put<ApiResponse<UserProfile>>('/api/profiles/profile', data)
    );
  }

  async deleteProfile(): Promise<void> {
    return this.handleRequest(
      this.api.delete<ApiResponse<void>>('/api/profiles/profile')
    );
  }

  async updateProfilePicture(data: UpdateProfilePictureRequest): Promise<UserProfile> {
    return this.handleRequest(
      this.api.put<ApiResponse<UserProfile>>('/api/profiles/profile/picture', data)
    );
  }

  async searchProfiles(params: ProfileSearchParams): Promise<PaginatedResponse<UserProfile>> {
    const queryString = this.buildQueryString(params);
    return this.handleRequest(
      this.api.get<ApiResponse<PaginatedResponse<UserProfile>>>(`/api/profiles/profiles/search?${queryString}`)
    );
  }

  async verifyProfile(userId: string): Promise<void> {
    return this.handleRequest(
      this.api.post<ApiResponse<void>>(`/api/profiles/profile/${userId}/verify`)
    );
  }

  // Address management
  async getAllAddresses(): Promise<Address[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<Address[]>>('/api/profiles/addresses')
    );
  }

  async getAddressById(addressId: string): Promise<Address> {
    return this.handleRequest(
      this.api.get<ApiResponse<Address>>(`/api/profiles/addresses/${addressId}`)
    );
  }

  async getDefaultAddress(): Promise<Address> {
    return this.handleRequest(
      this.api.get<ApiResponse<Address>>('/api/profiles/addresses/default')
    );
  }

  async addAddress(data: CreateAddressRequest): Promise<Address> {
    return this.handleRequest(
      this.api.post<ApiResponse<Address>>('/api/profiles/addresses', data)
    );
  }

  async updateAddress(addressId: string, data: UpdateAddressRequest): Promise<Address> {
    return this.handleRequest(
      this.api.put<ApiResponse<Address>>(`/api/profiles/addresses/${addressId}`, data)
    );
  }

  async deleteAddress(addressId: string): Promise<void> {
    return this.handleRequest(
      this.api.delete<ApiResponse<void>>(`/api/profiles/addresses/${addressId}`)
    );
  }

  async setDefaultAddress(addressId: string): Promise<void> {
    return this.handleRequest(
      this.api.put<ApiResponse<void>>(`/api/profiles/addresses/${addressId}/default`)
    );
  }

  // Preferences management
  async getPreferences(): Promise<UserPreferences> {
    return this.handleRequest(
      this.api.get<ApiResponse<UserPreferences>>('/api/profiles/preferences')
    );
  }

  async updatePreferences(data: UpdatePreferencesRequest): Promise<UserPreferences> {
    return this.handleRequest(
      this.api.put<ApiResponse<UserPreferences>>('/api/profiles/preferences', data)
    );
  }

  async resetPreferences(): Promise<UserPreferences> {
    return this.handleRequest(
      this.api.post<ApiResponse<UserPreferences>>('/api/profiles/preferences/reset')
    );
  }

  async checkHealth(): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>('/health')
    );
  }
}
