import { BaseService } from './base.service';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  RefreshTokenRequest,
  TokenValidationResponse,
  ApiResponse,
} from '../types';

export class AuthService extends BaseService {
  constructor(baseURL: string) {
    super(baseURL);
  }

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return this.handleRequest(
      this.api.post<ApiResponse<LoginResponse>>('/api/auth/login', credentials)
    );
  }

  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    return this.handleRequest(
      this.api.post<ApiResponse<RegisterResponse>>('/api/auth/register', userData)
    );
  }

  async logout(): Promise<void> {
    return this.handleRequest(
      this.api.post<ApiResponse<void>>('/api/auth/logout')
    );
  }

  async forgotPassword(data: ForgotPasswordRequest): Promise<void> {
    return this.handleRequest(
      this.api.post<ApiResponse<void>>('/api/auth/forgot-password', data)
    );
  }

  async resetPassword(data: ResetPasswordRequest): Promise<void> {
    return this.handleRequest(
      this.api.post<ApiResponse<void>>('/api/auth/reset-password', data)
    );
  }

  async changePassword(data: ChangePasswordRequest): Promise<void> {
    return this.handleRequest(
      this.api.post<ApiResponse<void>>('/api/auth/change-password', data)
    );
  }

  async refreshToken(data: RefreshTokenRequest): Promise<LoginResponse> {
    return this.handleRequest(
      this.api.post<ApiResponse<LoginResponse>>('/api/auth/refresh-token', data)
    );
  }

  async validateToken(): Promise<TokenValidationResponse> {
    return this.handleRequest(
      this.api.post<ApiResponse<TokenValidationResponse>>('/api/auth/validate')
    );
  }

  async checkHealth(): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>('/health')
    );
  }

  // Helper methods for token management
  setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem('token', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  clearTokens(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    return !!token;
  }
}
