import React, { useState } from 'react'

// Mock equipment data
const equipmentData = [
  {
    id: 1,
    name: '<PERSON> 8R Tractor',
    category: 'Tractor',
    condition: 'New',
    price: 325000,
    description: 'Latest model John Deere 8R Series Tractor with advanced precision technology and optimal fuel efficiency.',
    specs: {
      horsepower: 310,
      engine: '9.0L PowerTech™ PSS',
      transmission: 'e23™ PowerShift',
      weight: '26,500 lbs'
    },
    imageUrl: 'https://placehold.co/400x300',
    availability: 'In Stock',
    rating: 4.9
  },
  {
    id: 2,
    name: 'Case IH Combine Harvester',
    category: 'Harvester',
    condition: 'Used',
    price: 175000,
    description: 'Well-maintained Case IH Axial-Flow combine harvester. Excellent condition with recent maintenance.',
    specs: {
      horsepower: 380,
      engine: 'Case IH FPT 10.8L',
      grainTank: '350 bushels',
      hours: '2,200'
    },
    imageUrl: 'https://placehold.co/400x300',
    availability: 'In Stock',
    rating: 4.5
  },
  {
    id: 3,
    name: 'Kubota Compact Tractor',
    category: 'Tractor',
    condition: 'New',
    price: 29500,
    description: 'Versatile compact tractor perfect for small farms and specialty crops. Includes front loader attachment.',
    specs: {
      horsepower: 47,
      engine: '4-cylinder diesel',
      transmission: 'HST Plus',
      weight: '3,850 lbs'
    },
    imageUrl: 'https://placehold.co/400x300',
    availability: 'In Stock',
    rating: 4.7
  },
  {
    id: 4,
    name: 'Precision Planter',
    category: 'Planting Equipment',
    condition: 'New',
    price: 78000,
    description: 'High-precision planting system with individual row control and advanced seed placement technology.',
    specs: {
      rows: 16,
      spacing: 'Adjustable',
      technology: 'GPS-guided',
      compatibility: 'Universal mounting'
    },
    imageUrl: 'https://placehold.co/400x300',
    availability: 'Pre-order',
    rating: 4.8
  },
  {
    id: 5,
    name: 'Agricultural Drone',
    category: 'Technology',
    condition: 'New',
    price: 12500,
    description: 'Advanced agricultural drone for crop monitoring, mapping, and precision spraying applications.',
    specs: {
      flightTime: '40 minutes',
      range: '5 miles',
      camera: '48MP multispectral',
      payload: '2.5 gallons'
    },
    imageUrl: 'https://placehold.co/400x300',
    availability: 'In Stock',
    rating: 4.6
  },
  {
    id: 6,
    name: 'Irrigation System',
    category: 'Irrigation',
    condition: 'New',
    price: 35000,
    description: 'Complete center pivot irrigation system with smart controls and water conservation features.',
    specs: {
      coverage: '120 acres',
      flow: '800 GPM',
      controls: 'Smartphone app',
      powerSource: 'Electric/Solar'
    },
    imageUrl: 'https://placehold.co/400x300',
    availability: 'Made to Order',
    rating: 4.7
  }
]

// Equipment categories
const categories = [
  'All Categories',
  'Tractor',
  'Harvester',
  'Planting Equipment',
  'Technology',
  'Irrigation'
]

// Equipment conditions
const conditions = ['All Conditions', 'New', 'Used', 'Refurbished']

export default function EquipmentMarketplace() {
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedCondition, setSelectedCondition] = useState('All Conditions')
  const [priceRange, setPriceRange] = useState([0, 400000])
  
  // Filter equipment based on selections
  const filteredEquipment = equipmentData.filter(equipment => {
    const categoryMatch = selectedCategory === 'All Categories' || equipment.category === selectedCategory
    const conditionMatch = selectedCondition === 'All Conditions' || equipment.condition === selectedCondition
    const priceMatch = equipment.price >= priceRange[0] && equipment.price <= priceRange[1]
    return categoryMatch && conditionMatch && priceMatch
  })
  
  // Helper function to format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount)
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Equipment Marketplace</h1>
      <p className="text-lg mb-6">Find the right agricultural equipment for your farming operations</p>
      
      {/* Filters Section */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Filter Options</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-gray-700 mb-2">Equipment Type</label>
            <select 
              className="w-full p-2 border rounded"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-2">Condition</label>
            <select 
              className="w-full p-2 border rounded"
              value={selectedCondition}
              onChange={(e) => setSelectedCondition(e.target.value)}
            >
              {conditions.map(condition => (
                <option key={condition} value={condition}>{condition}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-2">Price Range</label>
            <div className="flex items-center space-x-2">
              <span>{formatCurrency(priceRange[0])}</span>
              <input 
                type="range" 
                min="0" 
                max="400000" 
                step="10000"
                value={priceRange[1]}
                onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                className="flex-1"
              />
              <span>{formatCurrency(priceRange[1])}</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Results Count */}
      <div className="mb-6">
        <p className="text-gray-600">Showing {filteredEquipment.length} results</p>
      </div>
      
      {/* Equipment Listings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {filteredEquipment.map(equipment => (
          <div key={equipment.id} className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col md:flex-row">
            <div className="md:w-2/5 h-56 md:h-auto relative">
              <img 
                src={equipment.imageUrl} 
                alt={equipment.name} 
                className="w-full h-full object-cover"
              />
              <span className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs text-white ${
                equipment.condition === 'New' 
                  ? 'bg-green-600' 
                  : equipment.condition === 'Used'
                  ? 'bg-blue-600'
                  : 'bg-purple-600'
              }`}>
                {equipment.condition}
              </span>
            </div>
            <div className="p-4 md:p-6 md:w-3/5">
              <div className="flex justify-between items-start">
                <h3 className="text-xl font-semibold">{equipment.name}</h3>
                <span className="text-xl font-bold text-green-700">{formatCurrency(equipment.price)}</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">{equipment.category}</p>
              <p className="text-gray-600 mt-3">{equipment.description}</p>
              
              <div className="mt-4 bg-gray-50 p-3 rounded-lg">
                <h4 className="font-medium text-sm mb-2">Specifications</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {Object.entries(equipment.specs).map(([key, value]) => (
                    <div key={key}>
                      <span className="text-gray-500">{key}: </span>
                      <span className="font-medium">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mt-4 flex justify-between items-center">
                <div className="flex items-center">
                  <div className="flex mr-1">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <span 
                        key={i} 
                        className={i < Math.floor(equipment.rating) ? "text-yellow-500" : "text-gray-300"}
                      >
                        ★
                      </span>
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">({equipment.rating})</span>
                </div>
                <span className={`text-sm px-2 py-1 rounded ${
                  equipment.availability === 'In Stock' 
                    ? 'bg-green-100 text-green-800' 
                    : equipment.availability === 'Pre-order' || equipment.availability === 'Made to Order'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {equipment.availability}
                </span>
              </div>
              
              <div className="mt-4 flex space-x-2">
                <button className="flex-1 bg-green-600 text-white py-2 rounded hover:bg-green-700 transition duration-200">
                  View Details
                </button>
                <button className="flex-1 border border-green-600 text-green-600 py-2 rounded hover:bg-green-50 transition duration-200">
                  Contact Seller
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Empty state */}
      {filteredEquipment.length === 0 && (
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <h3 className="text-xl font-semibold mb-2">No equipment found</h3>
          <p className="text-gray-600 mb-4">Try adjusting your filter settings to see more results.</p>
          <button 
            onClick={() => {
              setSelectedCategory('All Categories')
              setSelectedCondition('All Conditions')
              setPriceRange([0, 400000])
            }}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            Reset Filters
          </button>
        </div>
      )}
    </div>
  )
}
