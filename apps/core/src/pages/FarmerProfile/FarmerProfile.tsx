import React, { useState } from 'react'
import { useParams } from 'react-router-dom'

// Mock data for demonstration
const FARMER = {
  id: 101,
  name: '<PERSON>',
  location: 'Riverdale County, CA',
  experience: '15 years',
  bio: 'Third-generation farmer specializing in sustainable agriculture and organic vegetable cultivation. My farming practices focus on soil health, water conservation, and biodiversity.',
  rating: 4.8,
  reviewCount: 124,
  avatar: '/assets/farmer1.jpg',
  certification: ['Organic Certified', 'Sustainable Farming'],
  specialties: ['Vegetable Farming', 'Crop Rotation', 'Natural Pest Management'],
  contact: {
    email: '<EMAIL>',
    phone: '(*************'
  },
  plots: [
    {
      id: 1,
      name: 'Riverside Plot',
      size: '2.5 acres',
      price: 1200,
      soilQuality: 'High',
      organicCertified: true,
      image: '/assets/plot1.jpg',
      description: 'Rich riverside plot perfect for vegetable farming with excellent water access.'
    },
    {
      id: 2,
      name: 'Sunflower Field',
      size: '3.2 acres',
      price: 1500,
      soilQuality: 'High',
      organicCertified: true,
      image: '/assets/plot2.jpg',
      description: 'Sunny field with well-drained soil, ideal for sunflowers and other sun-loving crops.'
    }
  ],
  crops: [
    {
      id: 1,
      name: 'Organic Tomatoes',
      season: 'Summer',
      yield: 'High',
      image: '/assets/crop1.jpg'
    },
    {
      id: 2,
      name: 'Carrots',
      season: 'Spring & Fall',
      yield: 'Medium',
      image: '/assets/crop2.jpg'
    },
    {
      id: 3,
      name: 'Lettuce',
      season: 'Year-round',
      yield: 'High',
      image: '/assets/crop3.jpg'
    }
  ],
  farmingPractices: [
    {
      title: 'Crop Rotation',
      description: 'We rotate crops seasonally to maintain soil health and prevent pest buildup.'
    },
    {
      title: 'Natural Pest Management',
      description: 'We use beneficial insects and companion planting to control pests naturally.'
    },
    {
      title: 'Water Conservation',
      description: 'Drip irrigation and rainwater collection minimize water usage and maximize efficiency.'
    }
  ],
  stories: [
    {
      title: 'From Corporate to Farming',
      date: 'January 15, 2023',
      content: 'After 10 years in corporate finance, I returned to my family farm with new ideas for sustainable practices.',
      image: '/assets/story1.jpg'
    }
  ],
  gallery: [
    '/assets/gallery1.jpg',
    '/assets/gallery2.jpg',
    '/assets/gallery3.jpg',
    '/assets/gallery4.jpg'
  ],
  expertise: ['Organic Tomatoes', 'Carrots', 'Lettuce'],
  methods: ['Crop Rotation', 'Natural Pest Management', 'Water Conservation']
}

export default function FarmerProfile() {
  const { farmerId } = useParams<{ farmerId: string }>()
  const [activeTab, setActiveTab] = useState('plots')
  
  // In a real application, we would fetch the farmer data based on the ID
  // For now, we'll use our mock data
  const farmer = FARMER

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Farmer Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex flex-col md:flex-row items-center md:items-start">
          <div className="w-32 h-32 rounded-full overflow-hidden flex-shrink-0 mb-4 md:mb-0 md:mr-6">
            <img src={farmer.avatar} alt={farmer.name} className="w-full h-full object-cover" />
          </div>
          
          <div className="flex-grow text-center md:text-left">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-3xl font-bold mb-2">{farmer.name}</h1>
                <p className="text-gray-600 mb-2">{farmer.location} • {farmer.experience} Experience</p>
                
                <div className="flex flex-wrap justify-center md:justify-start mb-4">
                  {farmer.certification.map((cert, index) => (
                    <span key={index} className="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded mb-2">
                      {cert}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="flex flex-col items-center md:items-end">
                <div className="flex items-center mb-2">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 ${i < Math.floor(farmer.rating) ? 'text-yellow-400' : 'text-gray-300'}`} viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  <span className="text-gray-700 ml-1">{farmer.rating} ({farmer.reviewCount} reviews)</span>
                </div>
                
                <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition">
                  Contact Farmer
                </button>
              </div>
            </div>
            
            <p className="text-gray-700 mt-4">{farmer.bio}</p>
          </div>
        </div>
      </div>
      
      {/* Farmer Details Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('plots')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'plots'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Available Plots
            </button>
            <button
              onClick={() => setActiveTab('crops')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'crops'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Crops & Specialties
            </button>
            <button
              onClick={() => setActiveTab('practices')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'practices'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Farming Practices
            </button>
            <button
              onClick={() => setActiveTab('gallery')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'gallery'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Farm Gallery
            </button>
          </nav>
        </div>
      </div>
      
      {/* Tab Content */}
      <div className="mt-8">
        {/* Plots Tab */}
        {activeTab === 'plots' && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Available Plots</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {farmer.plots.map(plot => (
                <div key={plot.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="h-48 bg-gray-200">
                    <img src={plot.image} alt={plot.name} className="w-full h-full object-cover" />
                  </div>
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-xl font-semibold">{plot.name}</h3>
                      <span className="text-lg font-bold text-green-700">${plot.price}</span>
                    </div>
                    <p className="text-gray-700 mb-4">{plot.description}</p>
                    <div className="grid grid-cols-2 gap-2 mb-4">
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Size:</span> {plot.size}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Soil:</span> {plot.soilQuality}
                      </div>
                      {plot.organicCertified && (
                        <div className="text-sm col-span-2">
                          <span className="inline-flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            Organic Certified
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex justify-between items-start">
                      <button className="border border-[#22C55E] text-[#22C55E] px-4 py-2 rounded-full hover:bg-[#22C55E]/10">
                        View Cultivation Guide
                      </button>
                      <button className="bg-[#F97316] text-white px-4 py-2 rounded-full hover:bg-[#F97316]/90">
                        Start Growing
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Crops Tab */}
        {activeTab === 'crops' && (
          <div>
            <div className="mb-10">
              <h2 className="text-2xl font-bold mb-6">Specialties</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {farmer.specialties.map((specialty, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="font-medium">{specialty}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h2 className="text-2xl font-bold mb-6">Seasonal Crops</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {farmer.crops.map(crop => (
                  <div key={crop.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="h-40 bg-gray-200">
                      <img src={crop.image} alt={crop.name} className="w-full h-full object-cover" />
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold mb-2">{crop.name}</h3>
                      <div className="text-sm text-gray-600 mb-1">
                        <span className="font-medium">Season:</span> {crop.season}
                      </div>
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Expected Yield:</span> {crop.yield}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {/* Farming Practices Tab */}
        {activeTab === 'practices' && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Sustainable Farming Practices</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {farmer.farmingPractices.map((practice, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-md">
                  <h3 className="text-xl font-semibold mb-3">{practice.title}</h3>
                  <p className="text-gray-700">{practice.description}</p>
                </div>
              ))}
            </div>
            
            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Farming Expertise</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-lg shadow">
                  <h4 className="font-medium mb-2">Crop Specialization</h4>
                  <ul className="space-y-2">
                    {farmer.expertise.map((item, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-2 h-2 bg-[#22C55E] rounded-full mr-2"></span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="bg-white p-4 rounded-lg shadow">
                  <h4 className="font-medium mb-2">Growing Methods</h4>
                  <ul className="space-y-2">
                    {farmer.methods.map((method, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-2 h-2 bg-[#F97316] rounded-full mr-2"></span>
                        <span>{method}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Sustainable Practices</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {farmer.farmingPractices.map((practice, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-medium text-[#22C55E] mb-2">{practice.title}</h4>
                    <p className="text-gray-600">{practice.description}</p>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Farming Journey</h3>
              {farmer.stories.map((story, index) => (
                <div key={index} className="bg-white p-4 rounded-lg shadow mb-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-[#F97316]">{story.title}</h4>
                    <span className="text-sm text-gray-500">{story.date}</span>
                  </div>
                  <p className="text-gray-600">{story.content}</p>
                  {story.image && (
                    <img src={story.image} alt={story.title} className="mt-4 rounded-lg w-full h-48 object-cover" />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Gallery Tab */}
        {activeTab === 'gallery' && (
          <div>
            <h2 className="text-2xl font-bold mb-6">Farm Gallery</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              {farmer.gallery.map((image, index) => (
                <div key={index} className="bg-gray-200 rounded-lg overflow-hidden aspect-square">
                  <img src={image} alt={`Farm gallery ${index + 1}`} className="w-full h-full object-cover" />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Contact Information */}
      <div className="mt-12 bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-4">Contact Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span className="text-gray-700">{farmer.contact.email}</span>
          </div>
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            <span className="text-gray-700">{farmer.contact.phone}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
