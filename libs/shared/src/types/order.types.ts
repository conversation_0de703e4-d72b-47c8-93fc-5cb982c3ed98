import { BaseFilters, PaginatedResponse } from './common.types';
import { Product } from './product.types';
import { Address } from './profile.types';

// Order types
export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  items: OrderItem[];
  totalAmount: number;
  currency: string;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod: PaymentMethod;
  deliveryMethod: DeliveryMethod;
  shippingAddress?: Address;
  billingAddress?: Address;
  notes?: string;
  timeline: OrderTimeline[];
  tracking?: OrderTracking;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  sellerId: string;
  productType: string;
  productSnapshot: ProductSnapshot;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  qualityRequirements?: QualityRequirements;
  status: OrderItemStatus;
  notes?: string;
}

export interface ProductSnapshot {
  name: string;
  description: string;
  category: string;
  images: string[];
  specifications?: Record<string, any>;
}

export interface QualityRequirements {
  grade?: string;
  specifications?: string[];
  testingRequired?: boolean;
  customRequirements?: string;
}

export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PROCESSING = 'PROCESSING',
  READY_FOR_PICKUP = 'READY_FOR_PICKUP',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

export enum OrderItemStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PROCESSING = 'PROCESSING',
  READY = 'READY',
  SHIPPED = 'SHIPPED',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED'
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED'
}

export enum PaymentMethod {
  CASH_ON_DELIVERY = 'CASH_ON_DELIVERY',
  UPI = 'UPI',
  CREDIT_CARD = 'CREDIT_CARD',
  DEBIT_CARD = 'DEBIT_CARD',
  NET_BANKING = 'NET_BANKING',
  WALLET = 'WALLET',
  BANK_TRANSFER = 'BANK_TRANSFER'
}

export enum DeliveryMethod {
  PICKUP = 'PICKUP',
  HOME_DELIVERY = 'HOME_DELIVERY',
  WAREHOUSE_DELIVERY = 'WAREHOUSE_DELIVERY'
}

export interface OrderTimeline {
  status: OrderStatus;
  timestamp: string;
  notes?: string;
  updatedBy?: string;
}

export interface OrderTracking {
  trackingNumber?: string;
  carrier?: string;
  estimatedDelivery?: string;
  currentLocation?: string;
  trackingUrl?: string;
  updates: TrackingUpdate[];
}

export interface TrackingUpdate {
  status: string;
  location: string;
  timestamp: string;
  description: string;
}

// Order request types
export interface CreateOrderRequest {
  userId: string;
  items: CreateOrderItemRequest[];
  totalAmount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  deliveryMethod: DeliveryMethod;
  shippingAddressId?: string;
  billingAddressId?: string;
  notes?: string;
}

export interface CreateOrderItemRequest {
  productId: string;
  sellerId: string;
  productType: string;
  productSnapshot: ProductSnapshot;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  qualityRequirements?: QualityRequirements;
}

export interface UpdateOrderStatusRequest {
  status: OrderStatus;
  notes?: string;
}

export interface CancelOrderRequest {
  reason: string;
  notes?: string;
}

// Order filters and search
export interface OrderFilters extends BaseFilters {
  userId?: string;
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  paymentMethod?: PaymentMethod;
  deliveryMethod?: DeliveryMethod;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface OrderSearchParams extends BaseFilters {
  query?: string;
  userId?: string;
  sellerId?: string;
  status?: OrderStatus[];
  dateRange?: {
    from: string;
    to: string;
  };
}

// Order statistics
export interface OrderStats {
  totalOrders: number;
  totalAmount: number;
  averageOrderValue: number;
  statusBreakdown: Record<OrderStatus, number>;
  monthlyStats: MonthlyOrderStats[];
}

export interface MonthlyOrderStats {
  month: string;
  year: number;
  orderCount: number;
  totalAmount: number;
  averageValue: number;
}
