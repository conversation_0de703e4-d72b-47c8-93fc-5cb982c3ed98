import { Dispatch } from "redux";
import * as types from "./actionTypes";
import axiosConfig from "../../helpers/axiosConfig";
import { environment } from "../../helpers/environment";

export const setTheme = (theme: string) => {
  return (dispatch: Dispatch) => {
    dispatch({ type: types.SET_THEME, payload: theme });
  };
};

export const login_email_password = async (dispatch: Dispatch, payload: any) => {
  try {
    dispatch({ type: types.SIGN_IN, payload: { loading: true } });
    
    const headers = {
      "Content-Type": "application/json",
      "baseURL": environment.apiUrl
    }
    
    const response = await axiosConfig.post(`backend/user/login`, payload, { headers });
    
    dispatch({ 
      type: types.SIGN_IN, 
      payload: { 
        loading: false,
        data: response?.data?.status === 200 ? response.data?.data : null,
        success: response?.data?.status === 200,
        error: response?.data?.status !== 200 ? response.data?.meta : null
      } 
    });

    return response?.data?.data;
  } catch (error: any) {
    dispatch({ 
      type: types.SIGN_IN, 
      payload: { loading: false, success: false, error: error?.response?.data }
    });
    return error?.response?.data;
  }
};

export const signOut = (dispatch: Dispatch) => {
  dispatch({ type: types.SIGN_OUT });
};