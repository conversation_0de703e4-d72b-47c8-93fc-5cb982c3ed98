import { BaseService } from './base.service';
import {
  Order,
  CreateOrderRequest,
  UpdateOrderStatusRequest,
  CancelOrderRequest,
  OrderFilters,
  OrderSearchParams,
  PaginatedResponse,
  OrderStats,
  ApiResponse,
} from '../types';

export class OrderService extends BaseService {
  constructor(baseURL: string) {
    super(baseURL);
  }

  async getServiceInfo(): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>('/api')
    );
  }

  async getAllOrders(filters: OrderFilters = {}): Promise<PaginatedResponse<Order>> {
    const queryString = this.buildQueryString(filters);
    return this.handleRequest(
      this.api.get<ApiResponse<PaginatedResponse<Order>>>(`/api/orders?${queryString}`)
    );
  }

  async getOrderById(orderId: string): Promise<Order> {
    return this.handleRequest(
      this.api.get<ApiResponse<Order>>(`/api/orders/${orderId}`)
    );
  }

  async getOrdersByUserId(userId: string, filters: OrderFilters = {}): Promise<PaginatedResponse<Order>> {
    const queryString = this.buildQueryString(filters);
    return this.handleRequest(
      this.api.get<ApiResponse<PaginatedResponse<Order>>>(`/api/users/${userId}/orders?${queryString}`)
    );
  }

  async createOrder(data: CreateOrderRequest): Promise<Order> {
    return this.handleRequest(
      this.api.post<ApiResponse<Order>>('/api/orders', data)
    );
  }

  async updateOrderStatus(orderId: string, data: UpdateOrderStatusRequest): Promise<Order> {
    return this.handleRequest(
      this.api.patch<ApiResponse<Order>>(`/api/orders/${orderId}/status`, data)
    );
  }

  async cancelOrder(orderId: string, data: CancelOrderRequest): Promise<Order> {
    return this.handleRequest(
      this.api.post<ApiResponse<Order>>(`/api/orders/${orderId}/cancel`, data)
    );
  }

  async searchOrders(params: OrderSearchParams): Promise<PaginatedResponse<Order>> {
    const queryString = this.buildQueryString(params);
    return this.handleRequest(
      this.api.get<ApiResponse<PaginatedResponse<Order>>>(`/api/orders/search?${queryString}`)
    );
  }

  async getOrderStats(userId?: string): Promise<OrderStats> {
    const params = userId ? { userId } : {};
    const queryString = this.buildQueryString(params);
    return this.handleRequest(
      this.api.get<ApiResponse<OrderStats>>(`/api/orders/stats?${queryString}`)
    );
  }

  async trackOrder(orderId: string): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>(`/api/orders/${orderId}/track`)
    );
  }

  async getOrderInvoice(orderId: string): Promise<Blob> {
    const response = await this.api.get(`/api/orders/${orderId}/invoice`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async checkHealth(): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>('/health')
    );
  }
}
