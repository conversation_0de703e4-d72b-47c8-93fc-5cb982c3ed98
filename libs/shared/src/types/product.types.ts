import { BaseFilters, PaginatedResponse, Coordinates } from './common.types';

// Product types
export interface Product {
  id: string;
  name: string;
  description: string;
  category: ProductCategory;
  sellerId: string;
  sellerInfo?: SellerInfo;
  productType: ProductType;
  images: string[];
  pricing: ProductPricing;
  specifications: ProductSpecifications;
  availability: ProductAvailability;
  location: ProductLocation;
  qualityInfo: QualityInfo;
  harvestInfo?: HarvestInfo;
  certifications: string[];
  tags: string[];
  rating: number;
  reviewCount: number;
  viewCount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductCategory {
  id: string;
  name: string;
  code: string;
  parentCategory?: string;
}

export interface SellerInfo {
  id: string;
  name: string;
  rating: number;
  location: string;
  verified: boolean;
}

export enum ProductType {
  CROP = 'CROP',
  SEED = 'SEED',
  EQUIPMENT = 'EQUIPMENT',
  FERTILIZER = 'FERTILIZER',
  PESTICIDE = 'PESTICIDE'
}

export interface ProductPricing {
  basePrice: number;
  currency: string;
  unit: string;
  minOrderQuantity: number;
  maxOrderQuantity?: number;
  bulkPricing?: BulkPricing[];
  negotiable: boolean;
}

export interface BulkPricing {
  minQuantity: number;
  maxQuantity?: number;
  pricePerUnit: number;
  discount?: number;
}

export interface ProductSpecifications {
  variety?: string;
  grade?: string;
  size?: string;
  color?: string;
  moisture?: number;
  purity?: number;
  germination?: number;
  shelfLife?: string;
  storageConditions?: string;
  nutritionalInfo?: NutritionalInfo;
  technicalSpecs?: Record<string, any>;
}

export interface NutritionalInfo {
  protein?: number;
  carbohydrates?: number;
  fat?: number;
  fiber?: number;
  vitamins?: Record<string, number>;
  minerals?: Record<string, number>;
}

export interface ProductAvailability {
  inStock: boolean;
  quantity: number;
  unit: string;
  availableFrom: string;
  availableUntil?: string;
  leadTime?: number;
  restockDate?: string;
}

export interface ProductLocation {
  state: string;
  city: string;
  district?: string;
  pincode?: string;
  coordinates?: Coordinates;
  farmName?: string;
  farmSize?: number;
}

export interface QualityInfo {
  grade: string;
  qualityScore?: number;
  testReports?: TestReport[];
  qualityAssurance?: string[];
}

export interface TestReport {
  type: string;
  result: string;
  date: string;
  certifiedBy: string;
  documentUrl?: string;
}

export interface HarvestInfo {
  harvestDate: string;
  harvestSeason: string;
  farmingMethod: string;
  irrigationType?: string;
  soilType?: string;
  climateConditions?: string;
}

// Product filters
export interface ProductFilters extends BaseFilters {
  categoryId?: string;
  sellerId?: string;
  productType?: ProductType;
  minPrice?: number;
  maxPrice?: number;
  state?: string;
  city?: string;
  inStock?: boolean;
  certified?: boolean;
  grade?: string;
  harvestSeason?: string;
  farmingMethod?: string;
}

// Search types
export interface ProductSearchParams extends BaseFilters {
  query: string;
  productTypes?: ProductType[];
  categories?: string[];
  state?: string;
  city?: string;
  minPrice?: number;
  maxPrice?: number;
  includeAggregations?: boolean;
}

export interface ProductSearchResponse extends PaginatedResponse<Product> {
  aggregations?: SearchAggregations;
  suggestions?: string[];
}

export interface SearchAggregations {
  categories: AggregationBucket[];
  states: AggregationBucket[];
  cities: AggregationBucket[];
  priceRanges: AggregationBucket[];
  productTypes: AggregationBucket[];
}

export interface AggregationBucket {
  key: string;
  count: number;
}

// Dropdown types
export interface DropdownOption {
  id: string;
  name: string;
  code?: string;
  parentId?: string;
}

export interface CategoryDropdown extends DropdownOption {
  subcategories?: CategoryDropdown[];
}

// Product management types
export interface CreateProductRequest {
  name: string;
  description: string;
  categoryId: string;
  productType: ProductType;
  images: string[];
  pricing: ProductPricing;
  specifications: ProductSpecifications;
  availability: ProductAvailability;
  location: ProductLocation;
  qualityInfo: QualityInfo;
  harvestInfo?: HarvestInfo;
  certifications?: string[];
  tags?: string[];
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {
  id: string;
}
