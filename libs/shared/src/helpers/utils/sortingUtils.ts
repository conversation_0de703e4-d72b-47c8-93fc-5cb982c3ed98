/**
 * Sorts an array of objects based on a priority list and falls back to alphabetical sorting.
 *
 * @param items - Array of objects to sort
 * @param priorityList - List of values in order of priority
 * @param priorityKey - Key in each object to match against the priority list
 * @param alphaKey - Key to use for alphabetical sorting when priority is equal
 * @returns Sorted array
 */
export function sortByPriorityWithAlphaFallback<T extends Record<string, any>>(
    items: T[],
    priorityList: string[],
    priorityKey: keyof T,
    alphaKey: keyof T
  ): T[] {
    return [...items].sort((a, b) => {
      const priorityA = priorityList.indexOf(a[priorityKey]);
      const priorityB = priorityList.indexOf(b[priorityKey]);
      
      // If both items are in the priority list, sort by their order in the list
      if (priorityA !== -1 && priorityB !== -1) {
        return priorityA - priorityB;
      }
      
      // If only a is in the priority list, it comes first
      if (priorityA !== -1) {
        return -1;
      }
      
      // If only b is in the priority list, it comes first
      if (priorityB !== -1) {
        return 1;
      }
      
      // If neither is in the priority list, sort alphabetically
      return String(a[alphaKey]).localeCompare(String(b[alphaKey]));
    });
  }