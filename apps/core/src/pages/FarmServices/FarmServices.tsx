import React, { useState } from 'react'

// Mock service data
const servicesData = [
  {
    id: 1,
    name: 'Professional Plowing Service',
    category: 'Plowing',
    description: 'High-quality plowing with modern equipment for all soil types. Our expert operators ensure your fields are properly prepared for planting.',
    provider: 'AgriPlus Tilling',
    price: '$75/acre',
    rating: 4.8,
    imageUrl: 'https://placehold.co/300x200'
  },
  {
    id: 2,
    name: 'Crop Planting Service',
    category: 'Planting',
    description: 'Precision planting services using advanced technology to ensure optimal seed spacing and depth for maximum yield potential.',
    provider: 'PlantRight Inc.',
    price: '$50/acre',
    rating: 4.5,
    imageUrl: 'https://placehold.co/300x200'
  },
  {
    id: 3,
    name: 'Pest Management Program',
    category: 'Pest Management',
    description: 'Comprehensive pest management solutions using integrated approaches. We monitor, identify, and control pests while minimizing environmental impact.',
    provider: 'PestGuard Agriculture',
    price: '$30/acre/month',
    rating: 4.7,
    imageUrl: 'https://placehold.co/300x200'
  },
  {
    id: 4,
    name: 'Harvesting Services',
    category: 'Harvesting',
    description: 'Efficient harvesting services with state-of-the-art combines and equipment. We ensure timely harvest with minimal crop loss.',
    provider: 'Harvest Masters',
    price: '$90/acre',
    rating: 4.9,
    imageUrl: 'https://placehold.co/300x200'
  },
  {
    id: 5,
    name: 'Grain Transport',
    category: 'Transportation',
    description: 'Reliable and timely transportation of your harvested crops to storage facilities or markets. Our fleet ensures your produce arrives safely.',
    provider: 'Farm Freight Solutions',
    price: '$4/mile',
    rating: 4.6,
    imageUrl: 'https://placehold.co/300x200'
  },
  {
    id: 6,
    name: 'Agricultural Consulting',
    category: 'Consultation',
    description: 'Expert agricultural consulting services to optimize your farm operations. Get advice on crop selection, soil management, and yield improvement.',
    provider: 'AgriWise Consultants',
    price: '$150/hour',
    rating: 4.9,
    imageUrl: 'https://placehold.co/300x200'
  }
]

// Service categories
const categories = [
  'All Categories',
  'Plowing',
  'Planting',
  'Pest Management',
  'Harvesting',
  'Transportation',
  'Consultation'
]

export default function FarmServices() {
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [sortBy, setSortBy] = useState('rating')
  
  // Filter and sort services
  const filteredServices = servicesData
    .filter(service => 
      selectedCategory === 'All Categories' || service.category === selectedCategory
    )
    .sort((a, b) => {
      if (sortBy === 'rating') {
        return b.rating - a.rating
      }
      // Add more sorting options if needed
      return 0
    })
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Farm Services</h1>
      <p className="text-lg mb-6">Professional agricultural services to help you manage and optimize your farming operations</p>
      
      {/* Categories */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Service Categories</h2>
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <button
              key={category}
              className={`px-4 py-2 rounded-full ${
                selectedCategory === category 
                  ? 'bg-green-600 text-white' 
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>
      </div>
      
      {/* Sorting Options */}
      <div className="mb-6 flex justify-between items-center">
        <p className="text-gray-600">Showing {filteredServices.length} services</p>
        <div className="flex items-center">
          <span className="mr-2">Sort by:</span>
          <select 
            className="p-2 border rounded"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="rating">Top Rated</option>
            <option value="price">Price</option>
            <option value="name">Name</option>
          </select>
        </div>
      </div>
      
      {/* Service Listings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {filteredServices.map(service => (
          <div key={service.id} className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col md:flex-row">
            <div className="md:w-1/3 h-48 md:h-auto relative">
              <img 
                src={service.imageUrl} 
                alt={service.name} 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6 md:w-2/3">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-xl font-semibold">{service.name}</h3>
                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                  {service.category}
                </span>
              </div>
              <p className="text-gray-700 mb-4">{service.description}</p>
              <div className="flex justify-between items-center mb-4">
                <div>
                  <p className="text-gray-600">Provider: <span className="font-medium">{service.provider}</span></p>
                  <div className="flex items-center mt-1">
                    <div className="flex mr-1">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <span 
                          key={i} 
                          className={i < Math.floor(service.rating) ? "text-yellow-500" : "text-gray-300"}
                        >
                          ★
                        </span>
                      ))}
                    </div>
                    <span className="text-gray-600 text-sm">({service.rating})</span>
                  </div>
                </div>
                <p className="text-xl font-bold text-green-700">{service.price}</p>
              </div>
              <div className="flex space-x-2">
                <button className="flex-1 bg-green-600 text-white py-2 rounded hover:bg-green-700 transition duration-200">
                  Book Service
                </button>
                <button className="px-4 py-2 border border-green-600 text-green-600 rounded hover:bg-green-50 transition duration-200">
                  Contact Provider
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Empty state */}
      {filteredServices.length === 0 && (
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <h3 className="text-xl font-semibold mb-2">No services found</h3>
          <p className="text-gray-600 mb-4">No services found in this category. Please try another category.</p>
          <button 
            onClick={() => setSelectedCategory('All Categories')}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            Show All Services
          </button>
        </div>
      )}
    </div>
  )
}
