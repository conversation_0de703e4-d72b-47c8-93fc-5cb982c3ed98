
import { format, isValid, parse } from "date-fns";
import { twMerge } from "tailwind-merge";
import clsx, { ClassValue } from "clsx";
import axios from "axios";
import { toast } from "react-toastify";
import { environment } from "../environment";

declare global {
  interface Window {
    confirmHandler: (confirm: boolean) => void;
  }
}

export const getStatusSpan = (type = "") => {
  const status = type?.toLowerCase();
  let statusColorClass = "";
  if (
    status === "active" ||
    status === "true" ||
    status.includes("approved") ||
    status.includes("delivered") ||
    status.includes("offer in live")
  ) {
    statusColorClass = "bg-green-100 text-green-700 ring-green-600/20";
  } else if (["inactive", "pending"]?.includes?.(status)) {
    statusColorClass =
      "bg-gray-300   text-Black ring-gray-600/2 font-semibold ";
  } else if (status === "draft") {
    statusColorClass = "bg-yellow-100 text-yellow-700 ring-yellow-600/20";
  } else if (
    ["in-progress", "not-paid", "false", "offer going to live"]?.includes(status) ||
    status.includes("liquidated")
  ) {
    statusColorClass = "bg-orange-500 text-gray-800 font-medium  ring-0";
  } else if (
    ["cancelled", "offer expired"]?.includes(status) ||
    status.includes("liquidated")
  ) {
    statusColorClass = "bg-red-500 text-white  ";
  } else if (status === "accepted" || status.includes("initiated")) {
    statusColorClass = "bg-[#003C82] text-white ";
  } else if (status === "completed") {
    statusColorClass = "bg-[#048F0A] text-white ";
  } else if (status.includes("rejected") || status.includes("failed")) {
    statusColorClass = "bg-[#EE1111] text-white ";
  } else {
    statusColorClass = "bg-green-100 text-green-700 ring-green-600/20";
  }

  return (
    <span
      className={`inline-flex items-center rounded-md px-2 py-1 font-medium  ring-1 ring-inset ${statusColorClass}`}
    >
      {type}
    </span>
  );
};

export function maskSensitiveData(data: string): string {
  if (/^[^@]+@[^@]+\.[^@]+$/.test(data)) {
    const [name, domain] = data.split('@');
    const maskedName = name.charAt(0) + "***" + name.charAt(name.length - 1);
    return `${maskedName}@${domain}`;
  } else if (/^\d{10}$/.test(data)) { 
    return "*".repeat(data.length - 2) + data.slice(-2); 
  } else {
    return data; 
  }
}

export function scrollBottom(divId: string): void {
    const divElement: HTMLElement | null = document.getElementById(divId);
    if (divElement) {
        divElement.scrollTop = divElement.scrollHeight;
    }
}

export function ScrollToTop() {
  window.scrollTo({ top: 0 });
  // window.scrollTo({ top: 0, behavior: "smooth"});
}

export const ScrollToTopSmooth = () => {
  const c = document.documentElement.scrollTop || document.body.scrollTop;
  if (c > 0) {
    window.requestAnimationFrame(ScrollToTopSmooth);
    window.scrollTo(0, c - c / 8);
  }
};

export const ScrollToTargetSmooth = (targetElement: HTMLElement | null): void => {
    const targetY: number = (targetElement?.getBoundingClientRect()?.top ?? 0) + window.pageYOffset;
    const initialY: number = window.pageYOffset;
    // const distance: number = Math.abs(targetY - initialY);
    const duration: number = 1000; // Adjust the duration as needed

    // Function to perform smooth scrolling animation
    const animateScroll = (startTime: number): void => {
        const currentTime: number = Math.min(duration, Date.now() - startTime);
        const easeInOutCubic = (t: number): number => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        window.scrollTo(0, initialY + (targetY - initialY) * easeInOutCubic(currentTime / duration));

        // Continue scrolling until duration is reached
        if (currentTime < duration) {
            window.requestAnimationFrame(animateScroll.bind(null, startTime));
        }
    };

    // Start the smooth scrolling animation
    window.requestAnimationFrame(animateScroll.bind(null, Date.now()));
};

/**
 * Converts file size from KB to a readable format (KB, MB, GB).
 * @param {number} sizeInKB - File size in kilobytes.
 * @returns {string} - Formatted file size with units.
 */
export const formatFileSize = (sizeInKB: number): string => {
  if (sizeInKB < 1024) {
    return `${sizeInKB.toFixed(2)} KB`;
  } else if (sizeInKB < 1024 * 1024) {
    const sizeInMB = sizeInKB / 1024;
    return `${sizeInMB.toFixed(2)} MB`;
  } else {
    const sizeInGB = sizeInKB / (1024 * 1024);
    return `${sizeInGB.toFixed(2)} GB`;
  }
}

export const getFileExtension = (filename: string | undefined): { ext: string | undefined, filenameWithoutExt: string | undefined } => {
  if (filename) {
    const ext = filename.split(".").pop();
    const filenameWithoutExt = filename.split(".").slice(0, -1).join('.');
    return { ext, filenameWithoutExt };
  } else {
    return { ext: undefined, filenameWithoutExt: undefined };
  }
}

export const getVibrantColor = (index: number) => {
  const vibrantColors = [
    '#FFD700', // Gold
    '#40E0D0', // Turquoise
    '#7FFF00', // Chartreuse
    '#FF69B4', // Hot Pink
    '#00FA9A', // Medium Spring Green
    '#87CEFA', // Light Sky Blue
    '#FF6347', // Tomato
    '#00CED1', // Dark Turquoise
    '#FFB6C1', // Light Pink
    '#ADFF2F', // Green Yellow
    '#F0E68C', // Khaki
    '#98FB98', // Pale Green
    '#9370DB', // Medium Purple
    '#FF4500', // Orange Red
    '#DA70D6', // Orchid
    '#B0E0E6', // Powder Blue
  ];

  // Ensure index wraps around if it exceeds array length
  const validIndex = index % vibrantColors.length;
  
  return vibrantColors[validIndex];
};


  

export function dateFormatter(date : string){
  return format(new Date(date), "LLL dd, y - hh:mm aa")
}


export const getFileData = (file: File): Promise<any> => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    return new Promise((resolve, reject) => {
        reader.onload = () => {
        const base64File = reader.result as string;
        const filename = file.name;
        const filesize = file.size;
        const extension = file.type;
        resolve({ base64File, filename, filesize, extension });
        };
        reader.onerror = reject;
    });
};

export const stripExtension = (filename: string) => {
    return filename?.split('.').slice(0, -1).join('.');
}

export const handleLogout = (keepKeys: string[] = ['hasSeenTour'], reload = true) => {
  // Remove everything except the keys we want to keep
  const keysToRemove = Object.keys(localStorage).filter(key => !keepKeys.includes(key));
  keysToRemove.forEach(key => localStorage.removeItem(key));
  toast.success("Logged out successfully", {
    toastId: 'success1',
  });

  if (reload) {
    window.location.href = environment.basename + "/";
  }
};

export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

export const valiDateAndFormat = (dateString: string, inputFormat: string = 'yyyy-MM-dd', outputFormat: string = 'LLL dd, y') =>
    isValid(parse(dateString, inputFormat, new Date())) && dateString === format(parse(dateString, inputFormat, new Date()), inputFormat)
      ? format(parse(dateString, inputFormat, new Date()), outputFormat)
      : null;

export function formatPrice(price: number, showDecimals = false, currency = 'INR'): string {
// Handle non-numeric input
    if (isNaN(price)) {
        return "";
    }

    if (currency === "") currency = "INR";

    // For very small amounts (less than 0.01), show all decimal places
    if (Math.abs(price) > 0 && Math.abs(price) < 0.01) {
        const formatter = new Intl.NumberFormat(currency === "INR" ? 'en-IN' : "en-US", {
            style: 'currency',
            currency,
            minimumFractionDigits: 6,
            maximumFractionDigits: 6
        });
        return formatter.format(price);
    }

    // Use Intl.NumberFormat for locale-specific formatting
    const formatter = new Intl.NumberFormat(currency === "INR" ? 'en-IN' : "en-US", {
        style: 'currency',
        currency,
        minimumFractionDigits: showDecimals ? 2 : 0, // Control decimals
    });

    return formatter.format(price);
}


export function loadScript(src: string) {
    return new Promise((resolve) => {
        const script = document.createElement("script");
        script.src = src;
        script.onload = () => {
            resolve(true);
        };
        script.onerror = () => {
            resolve(false);
        };
        document.body.appendChild(script);
    });
}

export const getFileAsBlob = async (fileUrl: string) => {
    if (!fileUrl) return;
    try {
      const response = await axios.get(fileUrl, { responseType: "blob" });
      return new Blob([response.data]);
    } catch (error) {
        console.error(error);
    }
  };

export const confirmActionHandler = (setConfirmPopup: (open: boolean) => void) => {
    return new Promise<boolean>((resolve) => {
        setConfirmPopup?.(true);
        const handleConfirm = (confirm: boolean) => {
            setConfirmPopup?.(false);
            resolve(confirm);
        };

        window.confirmHandler = handleConfirm;
    });
};

export const haveCommonSubstring = (str1: string, str2: string, commonSubstring: string) => {
    return str1?.includes(commonSubstring) && str2?.includes(commonSubstring);
  };

export const getDateFromNow = (date: string) => {
    // return just now, few minutes ago, 1 hour ago, 2 hours ago, 3 hours ago, and then Today at 12:00 PM, and then the date
    const now = new Date();
    const dateToCompare = new Date(date);
    const diff = now.getTime() - dateToCompare.getTime();
    const diffInMinutes = Math.floor(diff / 60000);
    const diffInHours = Math.floor(diff / 3600000);
    const diffInDays = Math.floor(diff / 86400000);

    if (diffInMinutes < 1) {
        return "Just now";
    } else if (diffInMinutes < 60) {
        return `${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""} ago`;
    } else if (diffInHours < 24) {
        return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
    } else if (diffInDays < 1) {
        return `${format(dateToCompare, "hh:mm aa")}`;
    } else {
        return format(dateToCompare, "LLL dd, y");
    }
}