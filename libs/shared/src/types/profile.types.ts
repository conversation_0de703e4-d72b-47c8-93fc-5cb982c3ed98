import { Location, BaseFilters } from './common.types';

// Profile types
export interface UserProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  email: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  profilePicture?: string;
  bio?: string;
  interests?: string[];
  accountType: 'individual' | 'business';
  businessInfo?: BusinessInfo;
  preferences: UserPreferences;
  addresses: Address[];
  isVerified: boolean;
  verificationLevel: 'basic' | 'email' | 'phone' | 'document' | 'full';
  createdAt: string;
  updatedAt: string;
}

export interface BusinessInfo {
  businessName: string;
  businessType: string;
  registrationNumber?: string;
  taxId?: string;
  website?: string;
  description?: string;
  establishedYear?: number;
  employeeCount?: string;
  annualRevenue?: string;
  businessAddress?: Address;
}

export interface UserPreferences {
  language: string;
  currency: string;
  timezone: string;
  notifications: NotificationPreferences;
  privacy: PrivacyPreferences;
  delivery: DeliveryPreferences;
}

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  marketing: boolean;
  orderUpdates: boolean;
  priceAlerts: boolean;
}

export interface PrivacyPreferences {
  profileVisibility: 'public' | 'private' | 'contacts';
  showEmail: boolean;
  showPhone: boolean;
  allowDataCollection: boolean;
}

export interface DeliveryPreferences {
  preferredTimeSlots: string[];
  contactPreference: 'email' | 'phone' | 'both';
}

export interface Address {
  id: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  label: string;
  isDefault: boolean;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Profile request types
export interface CreateProfileRequest {
  firstName: string;
  lastName: string;
  displayName?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  phoneNumber?: string;
  bio?: string;
  interests?: string[];
  accountType?: 'individual' | 'business';
  businessInfo?: BusinessInfo;
}

export interface UpdateProfileRequest extends Partial<CreateProfileRequest> {}

export interface UpdateProfilePictureRequest {
  profilePicture: string;
}

// Address request types
export interface CreateAddressRequest {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  label: string;
  isDefault?: boolean;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface UpdateAddressRequest extends Partial<CreateAddressRequest> {}

// Profile search and filters
export interface ProfileSearchParams extends BaseFilters {
  query?: string;
  accountType?: 'individual' | 'business';
  isVerified?: boolean;
  city?: string;
  state?: string;
  country?: string;
}

// Preferences update types
export interface UpdatePreferencesRequest extends Partial<UserPreferences> {}

export interface UpdateNotificationPreferencesRequest extends Partial<NotificationPreferences> {}

export interface UpdatePrivacyPreferencesRequest extends Partial<PrivacyPreferences> {}

export interface UpdateDeliveryPreferencesRequest extends Partial<DeliveryPreferences> {}
