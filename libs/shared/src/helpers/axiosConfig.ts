import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosError, AxiosResponse } from "axios";
import { environment } from "./environment";

const axiosConfig: AxiosInstance = axios.create({
  baseURL: environment.apiUrl,
  timeout: 10000, // Adding reasonable timeout
  headers: {
    "Content-Type": "application/json",
  },
});

axiosConfig.interceptors.request.use(
  (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
    // Add auth token if available
    const token = localStorage.getItem("token");
    if (token && config.headers && !config.headers.Authorization) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if(config.headers && !config.headers.baseURL) {
      config.baseURL = environment.apiUrl;
    }

    return config;
  },
  (error: AxiosError): Promise<AxiosError> => {
    return Promise.reject(error);
  }
);

axiosConfig.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    return response;
  },
  (error: AxiosError): Promise<AxiosError> => {
    // More structured error handling
    if (error.response) {
      // Server responded with a status code outside of 2xx range
      console.error(`API Error: ${error.response.status}`, error.response.data);
    } else if (error.request) {
      // Request was made but no response received
      console.error("Network Error: No response received", error.request);
    } else {
      // Error in setting up the request
      console.error("Request Error:", error.message);
    }
    return Promise.reject(error);
  }
);

export default axiosConfig;