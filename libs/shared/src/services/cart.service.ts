import { BaseService } from './base.service';
import {
  Cart,
  AddToCartRequest,
  UpdateCartItemRequest,
  RemoveFromCartRequest,
  CartSummary,
  BulkCartOperation,
  ApiResponse,
} from '../types';

export class CartService extends BaseService {
  constructor(baseURL: string) {
    super(baseURL);
  }

  async getWelcomeMessage(): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>('/api')
    );
  }

  async getCart(userId: string): Promise<Cart> {
    return this.handleRequest(
      this.api.get<ApiResponse<Cart>>(`/api/cart/${userId}`)
    );
  }

  async addToCart(userId: string, data: AddToCartRequest): Promise<Cart> {
    return this.handleRequest(
      this.api.post<ApiResponse<Cart>>(`/api/cart/${userId}/items`, data)
    );
  }

  async updateCartItem(userId: string, itemId: string, data: UpdateCartItemRequest): Promise<Cart> {
    return this.handleRequest(
      this.api.put<ApiResponse<Cart>>(`/api/cart/${userId}/items/${itemId}`, data)
    );
  }

  async removeFromCart(userId: string, itemId: string): Promise<Cart> {
    return this.handleRequest(
      this.api.delete<ApiResponse<Cart>>(`/api/cart/${userId}/items/${itemId}`)
    );
  }

  async clearCart(userId: string): Promise<void> {
    return this.handleRequest(
      this.api.delete<ApiResponse<void>>(`/api/cart/${userId}`)
    );
  }

  async getCartSummary(userId: string): Promise<CartSummary> {
    return this.handleRequest(
      this.api.get<ApiResponse<CartSummary>>(`/api/cart/${userId}/summary`)
    );
  }

  async bulkCartOperation(userId: string, operation: BulkCartOperation): Promise<Cart> {
    return this.handleRequest(
      this.api.post<ApiResponse<Cart>>(`/api/cart/${userId}/bulk`, operation)
    );
  }

  async validateCartItems(userId: string): Promise<any> {
    return this.handleRequest(
      this.api.post<ApiResponse<any>>(`/api/cart/${userId}/validate`)
    );
  }

  async checkHealth(): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>('/health')
    );
  }
}
