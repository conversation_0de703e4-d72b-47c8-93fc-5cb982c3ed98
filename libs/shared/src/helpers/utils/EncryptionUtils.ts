import CryptoJS from 'crypto-js';
import { environment } from '@shared/config/environment';

// Define the key and IV (both must be 16 bytes for AES-128)
const key = CryptoJS.enc.Utf8.parse(environment.APP_API_ENCRYPTION_KEY); // 16 characters = 128-bit key
const iv = CryptoJS.enc.Utf8.parse(environment.APP_API_ENCRYPTION_KEY); // Initialization Vector

// Function to encrypt
export function encryptData(plainText: string) {
  const encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(plainText), key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.ciphertext.toString(CryptoJS.enc.Base64); // Return Base64 string
}

// Function to decrypt
export function decryptData(encryptedText: string) {
  const decrypted = CryptoJS.AES.decrypt(
    encryptedText,
    key,
    {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    }
  );
  return decrypted.toString(CryptoJS.enc.Utf8); // Convert to UTF-8
}