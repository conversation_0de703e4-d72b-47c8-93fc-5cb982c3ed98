import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { getProductService } from '../../services';
import {
  Product,
  ProductFilters,
  ProductSearchParams,
  ProductSearchResponse,
  PaginatedResponse,
  DropdownOption,
  CategoryDropdown,
  AsyncState,
} from '../../types';

interface ProductState {
  products: AsyncState<PaginatedResponse<Product>>;
  searchResults: AsyncState<ProductSearchResponse>;
  selectedProduct: AsyncState<Product>;
  trendingProducts: AsyncState<Product[]>;
  similarProducts: AsyncState<Product[]>;
  categories: AsyncState<CategoryDropdown[]>;
  states: AsyncState<DropdownOption[]>;
  cities: AsyncState<DropdownOption[]>;
  filters: ProductFilters;
  searchQuery: string;
}

const initialState: ProductState = {
  products: {
    data: null,
    isLoading: false,
    error: null,
  },
  searchResults: {
    data: null,
    isLoading: false,
    error: null,
  },
  selectedProduct: {
    data: null,
    isLoading: false,
    error: null,
  },
  trendingProducts: {
    data: null,
    isLoading: false,
    error: null,
  },
  similarProducts: {
    data: null,
    isLoading: false,
    error: null,
  },
  categories: {
    data: null,
    isLoading: false,
    error: null,
  },
  states: {
    data: null,
    isLoading: false,
    error: null,
  },
  cities: {
    data: null,
    isLoading: false,
    error: null,
  },
  filters: {},
  searchQuery: '',
};

// Async thunks
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (filters: ProductFilters = {}, { rejectWithValue }) => {
    try {
      const productService = getProductService();
      const response = await productService.getProducts(filters);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch products');
    }
  }
);

export const searchProducts = createAsyncThunk(
  'products/searchProducts',
  async (params: ProductSearchParams, { rejectWithValue }) => {
    try {
      const productService = getProductService();
      const response = await productService.searchProducts(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to search products');
    }
  }
);

export const fetchProductById = createAsyncThunk(
  'products/fetchProductById',
  async (productId: string, { rejectWithValue }) => {
    try {
      const productService = getProductService();
      const response = await productService.getProductById(productId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch product');
    }
  }
);

export const fetchTrendingProducts = createAsyncThunk(
  'products/fetchTrendingProducts',
  async (limit: number = 20, { rejectWithValue }) => {
    try {
      const productService = getProductService();
      const response = await productService.getTrendingProducts(limit);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch trending products');
    }
  }
);

export const fetchSimilarProducts = createAsyncThunk(
  'products/fetchSimilarProducts',
  async ({ productId, limit = 10 }: { productId: string; limit?: number }, { rejectWithValue }) => {
    try {
      const productService = getProductService();
      const response = await productService.getSimilarProducts(productId, limit);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch similar products');
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'products/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const productService = getProductService();
      const response = await productService.getCategories();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

export const fetchStates = createAsyncThunk(
  'products/fetchStates',
  async (_, { rejectWithValue }) => {
    try {
      const productService = getProductService();
      const response = await productService.getStates();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch states');
    }
  }
);

export const fetchCitiesByState = createAsyncThunk(
  'products/fetchCitiesByState',
  async (stateId: string, { rejectWithValue }) => {
    try {
      const productService = getProductService();
      const response = await productService.getCitiesByState(stateId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch cities');
    }
  }
);

const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<ProductFilters>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    clearSearchResults: (state) => {
      state.searchResults = {
        data: null,
        isLoading: false,
        error: null,
      };
    },
    clearSelectedProduct: (state) => {
      state.selectedProduct = {
        data: null,
        isLoading: false,
        error: null,
      };
    },
    clearErrors: (state) => {
      state.products.error = null;
      state.searchResults.error = null;
      state.selectedProduct.error = null;
      state.trendingProducts.error = null;
      state.similarProducts.error = null;
      state.categories.error = null;
      state.states.error = null;
      state.cities.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch products
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.products.isLoading = true;
        state.products.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.products.isLoading = false;
        state.products.data = action.payload;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.products.isLoading = false;
        state.products.error = action.payload as string;
      });

    // Search products
    builder
      .addCase(searchProducts.pending, (state) => {
        state.searchResults.isLoading = true;
        state.searchResults.error = null;
      })
      .addCase(searchProducts.fulfilled, (state, action) => {
        state.searchResults.isLoading = false;
        state.searchResults.data = action.payload;
      })
      .addCase(searchProducts.rejected, (state, action) => {
        state.searchResults.isLoading = false;
        state.searchResults.error = action.payload as string;
      });

    // Fetch product by ID
    builder
      .addCase(fetchProductById.pending, (state) => {
        state.selectedProduct.isLoading = true;
        state.selectedProduct.error = null;
      })
      .addCase(fetchProductById.fulfilled, (state, action) => {
        state.selectedProduct.isLoading = false;
        state.selectedProduct.data = action.payload;
      })
      .addCase(fetchProductById.rejected, (state, action) => {
        state.selectedProduct.isLoading = false;
        state.selectedProduct.error = action.payload as string;
      });

    // Fetch trending products
    builder
      .addCase(fetchTrendingProducts.pending, (state) => {
        state.trendingProducts.isLoading = true;
        state.trendingProducts.error = null;
      })
      .addCase(fetchTrendingProducts.fulfilled, (state, action) => {
        state.trendingProducts.isLoading = false;
        state.trendingProducts.data = action.payload;
      })
      .addCase(fetchTrendingProducts.rejected, (state, action) => {
        state.trendingProducts.isLoading = false;
        state.trendingProducts.error = action.payload as string;
      });

    // Fetch similar products
    builder
      .addCase(fetchSimilarProducts.pending, (state) => {
        state.similarProducts.isLoading = true;
        state.similarProducts.error = null;
      })
      .addCase(fetchSimilarProducts.fulfilled, (state, action) => {
        state.similarProducts.isLoading = false;
        state.similarProducts.data = action.payload;
      })
      .addCase(fetchSimilarProducts.rejected, (state, action) => {
        state.similarProducts.isLoading = false;
        state.similarProducts.error = action.payload as string;
      });

    // Fetch categories
    builder
      .addCase(fetchCategories.pending, (state) => {
        state.categories.isLoading = true;
        state.categories.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories.isLoading = false;
        state.categories.data = action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.categories.isLoading = false;
        state.categories.error = action.payload as string;
      });

    // Fetch states
    builder
      .addCase(fetchStates.pending, (state) => {
        state.states.isLoading = true;
        state.states.error = null;
      })
      .addCase(fetchStates.fulfilled, (state, action) => {
        state.states.isLoading = false;
        state.states.data = action.payload;
      })
      .addCase(fetchStates.rejected, (state, action) => {
        state.states.isLoading = false;
        state.states.error = action.payload as string;
      });

    // Fetch cities by state
    builder
      .addCase(fetchCitiesByState.pending, (state) => {
        state.cities.isLoading = true;
        state.cities.error = null;
      })
      .addCase(fetchCitiesByState.fulfilled, (state, action) => {
        state.cities.isLoading = false;
        state.cities.data = action.payload;
      })
      .addCase(fetchCitiesByState.rejected, (state, action) => {
        state.cities.isLoading = false;
        state.cities.error = action.payload as string;
      });
  },
});

export const {
  setFilters,
  clearFilters,
  setSearchQuery,
  clearSearchResults,
  clearSelectedProduct,
  clearErrors,
} = productSlice.actions;

export default productSlice.reducer;
