
interface DataReducersState {
  data?: any;
  meta?: any;
  loading?: boolean;
  success?: boolean;
  message?: string | null;
}


export const initialState :{ [key: string]: DataReducersState } = {
  theme: {
    data: "dark"
  },
  user_details: {
    data: {},
    meta: {},
  },
  user_token: {
    data: "",
    meta: {},
  },
};

const DataReducers = (state = initialState, action: any) => {
  switch (action.type) {
    case "SET_THEME":
      return {
        ...state,
        theme: action.payload,
      };
    case "SET_USER_DATA":
      return {
        ...state,
        user_details: action.payload,
      };
    case "SET_USER_TOKEN":
      return {
        ...state,
        user_token: action.payload,
      };
    default:
      return state;
  }
};

export default DataReducers;
