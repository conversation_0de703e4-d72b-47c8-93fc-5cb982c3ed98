import React, { useState } from 'react'

// Mock calendar data
const calendarEvents = [
  { id: 1, title: 'Planting - Corn', start: '2023-04-15', end: '2023-04-25', category: 'planting', crop: 'corn' },
  { id: 2, title: 'Fertilizing - Wheat', start: '2023-04-20', end: '2023-04-22', category: 'maintenance', crop: 'wheat' },
  { id: 3, title: 'Harvesting - Soybeans', start: '2023-10-10', end: '2023-10-20', category: 'harvesting', crop: 'soybeans' },
  { id: 4, title: 'Planting - Wheat', start: '2023-09-15', end: '2023-09-30', category: 'planting', crop: 'wheat' },
  { id: 5, title: 'Pest Control - Corn', start: '2023-06-10', end: '2023-06-12', category: 'maintenance', crop: 'corn' },
  { id: 6, title: 'Equipment Maintenance', start: '2023-07-05', end: '2023-07-07', category: 'maintenance', crop: 'all' },
  { id: 7, title: 'Agricultural Fair', start: '2023-08-20', end: '2023-08-22', category: 'event', crop: 'all' },
  { id: 8, title: 'Irrigation Check', start: '2023-05-15', end: '2023-05-16', category: 'maintenance', crop: 'all' }
]

// Month data for the calendar
const months = [
  { name: 'January', shortName: 'Jan', days: 31, season: 'Winter' },
  { name: 'February', shortName: 'Feb', days: 28, season: 'Winter' },
  { name: 'March', shortName: 'Mar', days: 31, season: 'Spring' },
  { name: 'April', shortName: 'Apr', days: 30, season: 'Spring' },
  { name: 'May', shortName: 'May', days: 31, season: 'Spring' },
  { name: 'June', shortName: 'Jun', days: 30, season: 'Summer' },
  { name: 'July', shortName: 'Jul', days: 31, season: 'Summer' },
  { name: 'August', shortName: 'Aug', days: 31, season: 'Summer' },
  { name: 'September', shortName: 'Sep', days: 30, season: 'Fall' },
  { name: 'October', shortName: 'Oct', days: 31, season: 'Fall' },
  { name: 'November', shortName: 'Nov', days: 30, season: 'Fall' },
  { name: 'December', shortName: 'Dec', days: 31, season: 'Winter' }
]

// Crop planting guidance
const cropGuidance = [
  {
    crop: 'Corn',
    plantingSeason: 'Spring',
    idealMonths: ['April', 'May'],
    growingDays: '90-120 days',
    soilTemp: '50-55°F (10-13°C)',
    notes: 'Plant after last frost when soil is warm. Requires regular watering and nitrogen-rich soil.'
  },
  {
    crop: 'Wheat',
    plantingSeason: 'Fall/Spring',
    idealMonths: ['September', 'October', 'March', 'April'],
    growingDays: '180-240 days (winter wheat), 90-120 days (spring wheat)',
    soilTemp: '40-45°F (4-7°C)',
    notes: 'Winter wheat planted in fall, spring wheat planted in early spring. Drought resistant once established.'
  },
  {
    crop: 'Soybeans',
    plantingSeason: 'Spring',
    idealMonths: ['May', 'June'],
    growingDays: '80-120 days',
    soilTemp: '60-70°F (15-21°C)',
    notes: 'Plant after all danger of frost. Fixes its own nitrogen but needs well-drained soil.'
  },
  {
    crop: 'Cotton',
    plantingSeason: 'Spring',
    idealMonths: ['April', 'May'],
    growingDays: '160-180 days',
    soilTemp: '60°F (15°C)',
    notes: 'Long growing season required. Sensitive to frost. Needs hot weather to mature properly.'
  }
]

export default function AgricultureCalendar() {
  const [selectedMonth, setSelectedMonth] = useState(3) // April
  const [selectedCrop, setSelectedCrop] = useState('all')
  const [selectedCategory, setSelectedCategory] = useState('all')
  
  // Get the current month's events
  const currentMonthEvents = calendarEvents.filter(event => {
    const eventMonth = new Date(event.start).getMonth()
    const cropMatch = selectedCrop === 'all' || event.crop === selectedCrop
    const categoryMatch = selectedCategory === 'all' || event.category === selectedCategory
    return eventMonth === selectedMonth && cropMatch && categoryMatch
  })
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Agricultural Calendar</h1>
      <p className="text-lg mb-6">Plan your farming activities with our seasonal calendar and crop rotation tools</p>
      
      {/* Season Indicator */}
      <div className="bg-bg-paper p-4 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Current Growing Season</h2>
        <div className="flex flex-wrap">
          {months.map((month, index) => (
            <div 
              key={month.name}
              className={`flex-1 min-w-[100px] p-3 text-center cursor-pointer border-b-4 ${
                index === selectedMonth 
                  ? 'border-primary bg-secondary-light font-medium' 
                  : month.season === 'Spring' 
                    ? 'border-secondary hover:bg-secondary-light'
                    : month.season === 'Summer'
                    ? 'border-yellow-200 hover:bg-yellow-50'
                    : month.season === 'Fall'
                    ? 'border-orange-200 hover:bg-orange-50'
                    : 'border-blue-200 hover:bg-blue-50'
              }`}
              onClick={() => setSelectedMonth(index)}
            >
              <div>{month.name}</div>
              <div className="text-xs text-gray-500">{month.season}</div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div>
          <label className="block text-gray-700 mb-2">Crop Type</label>
          <select 
            className="w-48 p-2 border rounded focus:ring-primary focus:border-primary"
            value={selectedCrop}
            onChange={(e) => setSelectedCrop(e.target.value)}
          >
            <option value="all">All Crops</option>
            <option value="corn">Corn</option>
            <option value="wheat">Wheat</option>
            <option value="soybeans">Soybeans</option>
            <option value="cotton">Cotton</option>
          </select>
        </div>
        
        <div>
          <label className="block text-gray-700 mb-2">Activity Type</label>
          <select 
            className="w-48 p-2 border rounded focus:ring-primary focus:border-primary"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="all">All Activities</option>
            <option value="planting">Planting</option>
            <option value="harvesting">Harvesting</option>
            <option value="maintenance">Maintenance</option>
            <option value="event">Events</option>
          </select>
        </div>
      </div>
      
      {/* Calendar View */}
      <div className="bg-bg-paper rounded-lg shadow-md overflow-hidden mb-8">
        <div className="p-4 bg-primary text-white flex justify-between items-center">
          <h2 className="text-xl font-semibold">{months[selectedMonth].name} Calendar</h2>
          <div className="flex space-x-2">
            <button className="btn-secondary">
              Add Event
            </button>
            <button className="btn-secondary">
              Export
            </button>
          </div>
        </div>
        
        {/* Calendar Grid - Simplified version */}
        <div className="p-4">
          <div className="grid grid-cols-7 gap-1 mb-2 text-center">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="p-2 text-sm font-medium bg-gray-100">
                {day}
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-7 gap-1 text-center">
            {Array.from({ length: 35 }).map((_, i) => {
              const dayNumber = i - 6 + 1 // Adjust for month starting on a specific day
              return (
                <div 
                  key={i} 
                  className={`p-2 min-h-[80px] border ${
                    dayNumber > 0 && dayNumber <= months[selectedMonth].days 
                      ? 'bg-white' 
                      : 'bg-gray-50 text-gray-400'
                  }`}
                >
                  {dayNumber > 0 && dayNumber <= months[selectedMonth].days && (
                    <>
                      <div className="text-right">{dayNumber}</div>
                      {/* Calendar events would be populated here */}
                      {i % 7 === 3 && dayNumber === 15 && (
                        <div className="text-xs mt-1 p-1 bg-primary-light text-primary-contrast rounded">
                          Planting - Corn
                        </div>
                      )}
                      {i % 8 === 4 && dayNumber === 20 && (
                        <div className="text-xs mt-1 p-1 bg-secondary text-secondary-contrast rounded">
                          Fertilizing
                        </div>
                      )}
                    </>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      </div>
      
      {/* Month Events */}
      <div className="bg-bg-paper p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Scheduled Activities for {months[selectedMonth].name}</h2>
        
        {currentMonthEvents.length > 0 ? (
          <div className="space-y-3">
            {currentMonthEvents.map(event => (
              <div key={event.id} className="flex border-l-4 border-primary bg-gray-50 p-3 rounded">
                <div className="flex-1">
                  <h3 className="font-medium">{event.title}</h3>
                  <p className="text-sm text-gray-600">
                    {new Date(event.start).toLocaleDateString()} - {new Date(event.end).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="px-3 py-1 bg-secondary text-secondary-contrast rounded text-sm hover:bg-secondary-dark">
                    Edit
                  </button>
                  <button className="px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200">
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No activities scheduled for this month with the selected filters.</p>
            <button className="mt-4 btn-primary">
              Add New Activity
            </button>
          </div>
        )}
      </div>
      
      {/* Crop Planting Guide */}
      <div className="bg-bg-paper p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Crop Planting Guide</h2>
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            <thead>
              <tr className="bg-secondary-light">
                <th className="px-4 py-2 text-left">Crop</th>
                <th className="px-4 py-2 text-left">Season</th>
                <th className="px-4 py-2 text-left">Ideal Months</th>
                <th className="px-4 py-2 text-left">Growing Period</th>
                <th className="px-4 py-2 text-left">Soil Temperature</th>
                <th className="px-4 py-2 text-left">Notes</th>
              </tr>
            </thead>
            <tbody>
              {cropGuidance.map((crop, index) => (
                <tr key={crop.crop} className={index < cropGuidance.length - 1 ? 'border-b' : ''}>
                  <td className="px-4 py-3 font-medium">{crop.crop}</td>
                  <td className="px-4 py-3">{crop.plantingSeason}</td>
                  <td className="px-4 py-3">{crop.idealMonths.join(', ')}</td>
                  <td className="px-4 py-3">{crop.growingDays}</td>
                  <td className="px-4 py-3">{crop.soilTemp}</td>
                  <td className="px-4 py-3 text-sm">{crop.notes}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-4 flex justify-between items-center">
          <p className="text-sm text-gray-500">Source: Agricultural Extension Service</p>
          <button className="btn-secondary">
            View Complete Guide
          </button>
        </div>
      </div>
    </div>
  )
}
