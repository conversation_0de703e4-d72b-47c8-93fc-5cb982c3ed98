import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { getCartService } from '../../services';
import {
  Cart,
  AddToCartRequest,
  UpdateCartItemRequest,
  CartSummary,
  BulkCartOperation,
  AsyncState,
} from '../../types';

interface CartState {
  cart: AsyncState<Cart>;
  summary: AsyncState<CartSummary>;
  userId: string | null;
}

const initialState: CartState = {
  cart: {
    data: null,
    isLoading: false,
    error: null,
  },
  summary: {
    data: null,
    isLoading: false,
    error: null,
  },
  userId: null,
};

// Async thunks
export const fetchCart = createAsyncThunk(
  'cart/fetchCart',
  async (userId: string, { rejectWithValue }) => {
    try {
      const cartService = getCartService();
      const response = await cartService.getCart(userId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch cart');
    }
  }
);

export const addToCart = createAsyncThunk(
  'cart/addToCart',
  async ({ userId, item }: { userId: string; item: AddToCartRequest }, { rejectWithValue }) => {
    try {
      const cartService = getCartService();
      const response = await cartService.addToCart(userId, item);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to add item to cart');
    }
  }
);

export const updateCartItem = createAsyncThunk(
  'cart/updateCartItem',
  async (
    { userId, itemId, updates }: { userId: string; itemId: string; updates: UpdateCartItemRequest },
    { rejectWithValue }
  ) => {
    try {
      const cartService = getCartService();
      const response = await cartService.updateCartItem(userId, itemId, updates);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update cart item');
    }
  }
);

export const removeFromCart = createAsyncThunk(
  'cart/removeFromCart',
  async ({ userId, itemId }: { userId: string; itemId: string }, { rejectWithValue }) => {
    try {
      const cartService = getCartService();
      const response = await cartService.removeFromCart(userId, itemId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to remove item from cart');
    }
  }
);

export const clearCart = createAsyncThunk(
  'cart/clearCart',
  async (userId: string, { rejectWithValue }) => {
    try {
      const cartService = getCartService();
      await cartService.clearCart(userId);
      return null;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to clear cart');
    }
  }
);

export const fetchCartSummary = createAsyncThunk(
  'cart/fetchCartSummary',
  async (userId: string, { rejectWithValue }) => {
    try {
      const cartService = getCartService();
      const response = await cartService.getCartSummary(userId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch cart summary');
    }
  }
);

export const bulkCartOperation = createAsyncThunk(
  'cart/bulkCartOperation',
  async (
    { userId, operation }: { userId: string; operation: BulkCartOperation },
    { rejectWithValue }
  ) => {
    try {
      const cartService = getCartService();
      const response = await cartService.bulkCartOperation(userId, operation);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to perform bulk cart operation');
    }
  }
);

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    setUserId: (state, action: PayloadAction<string>) => {
      state.userId = action.payload;
    },
    clearCartState: (state) => {
      state.cart = {
        data: null,
        isLoading: false,
        error: null,
      };
      state.summary = {
        data: null,
        isLoading: false,
        error: null,
      };
    },
    clearErrors: (state) => {
      state.cart.error = null;
      state.summary.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch cart
    builder
      .addCase(fetchCart.pending, (state) => {
        state.cart.isLoading = true;
        state.cart.error = null;
      })
      .addCase(fetchCart.fulfilled, (state, action) => {
        state.cart.isLoading = false;
        state.cart.data = action.payload;
      })
      .addCase(fetchCart.rejected, (state, action) => {
        state.cart.isLoading = false;
        state.cart.error = action.payload as string;
      });

    // Add to cart
    builder
      .addCase(addToCart.pending, (state) => {
        state.cart.isLoading = true;
        state.cart.error = null;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.cart.isLoading = false;
        state.cart.data = action.payload;
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.cart.isLoading = false;
        state.cart.error = action.payload as string;
      });

    // Update cart item
    builder
      .addCase(updateCartItem.pending, (state) => {
        state.cart.isLoading = true;
        state.cart.error = null;
      })
      .addCase(updateCartItem.fulfilled, (state, action) => {
        state.cart.isLoading = false;
        state.cart.data = action.payload;
      })
      .addCase(updateCartItem.rejected, (state, action) => {
        state.cart.isLoading = false;
        state.cart.error = action.payload as string;
      });

    // Remove from cart
    builder
      .addCase(removeFromCart.pending, (state) => {
        state.cart.isLoading = true;
        state.cart.error = null;
      })
      .addCase(removeFromCart.fulfilled, (state, action) => {
        state.cart.isLoading = false;
        state.cart.data = action.payload;
      })
      .addCase(removeFromCart.rejected, (state, action) => {
        state.cart.isLoading = false;
        state.cart.error = action.payload as string;
      });

    // Clear cart
    builder
      .addCase(clearCart.pending, (state) => {
        state.cart.isLoading = true;
        state.cart.error = null;
      })
      .addCase(clearCart.fulfilled, (state) => {
        state.cart.isLoading = false;
        state.cart.data = null;
      })
      .addCase(clearCart.rejected, (state, action) => {
        state.cart.isLoading = false;
        state.cart.error = action.payload as string;
      });

    // Fetch cart summary
    builder
      .addCase(fetchCartSummary.pending, (state) => {
        state.summary.isLoading = true;
        state.summary.error = null;
      })
      .addCase(fetchCartSummary.fulfilled, (state, action) => {
        state.summary.isLoading = false;
        state.summary.data = action.payload;
      })
      .addCase(fetchCartSummary.rejected, (state, action) => {
        state.summary.isLoading = false;
        state.summary.error = action.payload as string;
      });

    // Bulk cart operation
    builder
      .addCase(bulkCartOperation.pending, (state) => {
        state.cart.isLoading = true;
        state.cart.error = null;
      })
      .addCase(bulkCartOperation.fulfilled, (state, action) => {
        state.cart.isLoading = false;
        state.cart.data = action.payload;
      })
      .addCase(bulkCartOperation.rejected, (state, action) => {
        state.cart.isLoading = false;
        state.cart.error = action.payload as string;
      });
  },
});

export const { setUserId, clearCartState, clearErrors } = cartSlice.actions;
export default cartSlice.reducer;
