import { jwtDecode } from "jwt-decode";

export interface DecodedToken {
  id?: string | number;
  farmer_id?: string;
  exp?: number;
  name?: string;
  email?: string;
  mobile_number?: string;
  role?: {
    code: string;
    name: string;
  };
}

export interface Environment {
  mode: 'development' | 'production';
  token: string;
  tokenData: DecodedToken;

  apiUrl: string;
  
  authApiUrl: string;
  productApiUrl: string;
  orderApiUrl: string;
  cartApiUrl: string;
  searchApiUrl: string;
  profileApiUrl: string;
}



const decoded_token: DecodedToken = (() => {
  const token = localStorage.getItem("token");
  if (!token) return {};
  try {
    const decodedToken:any = jwtDecode(token);
    return {
      ...decodedToken,
      userType: decodedToken?.roles?.code,
    };
  } catch (error) {
    console.warn('Failed to decode JWT token:', error);
    return {};
  }
})();

export const environment: Environment = {
  mode: import.meta.env.MODE as 'development' | 'production',
  apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  token: localStorage.getItem("token") || '',
  tokenData: decoded_token,

  authApiUrl: import.meta.env.VITE_AUTH_API_URL || 'http://localhost:3000',
  productApiUrl: import.meta.env.VITE_PRODUCT_API_URL || 'http://localhost:6002',
  orderApiUrl: import.meta.env.VITE_ORDER_API_URL || 'http://localhost:3001',
  cartApiUrl: import.meta.env.VITE_CART_API_URL || 'http://localhost:3333',
  searchApiUrl: import.meta.env.VITE_SEARCH_API_URL || 'http://localhost:6002',
  profileApiUrl: import.meta.env.VITE_PROFILE_API_URL || 'http://localhost:6006',
};

export const getEnvironment = () => {
  return {
    // Main API URL (Auth service)
    API_URL: import.meta.env.VITE_API_URL || 'http://localhost:3000',

    // Individual service URLs
    AUTH_API_URL: import.meta.env.VITE_AUTH_API_URL || 'http://localhost:3000',
    PRODUCT_API_URL: import.meta.env.VITE_PRODUCT_API_URL || 'http://localhost:6002',
    PROFILE_API_URL: import.meta.env.VITE_PROFILE_API_URL || 'http://localhost:6006',
    ORDER_API_URL: import.meta.env.VITE_ORDER_API_URL || 'http://localhost:3001',
    CART_API_URL: import.meta.env.VITE_CART_API_URL || 'http://localhost:3333',

    // Other environment variables
    JWT_SECRET: import.meta.env.VITE_JWT_SECRET || 'your-secret-key',
    NODE_ENV: import.meta.env.NODE_ENV || 'development',
    APP_NAME: import.meta.env.VITE_APP_NAME || 'AgriTech Buyer',
    APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  };
};

export const decodeToken = (token: string): DecodedToken | null => {
  try {
    return jwtDecode<DecodedToken>(token);
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};