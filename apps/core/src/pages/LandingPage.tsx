import React from 'react'
import { Link } from 'react-router-dom'

export default function LandingPage() {
  return (
    <div className="bg-gradient-hero min-h-screen">
      {/* Hero Section */}
      <div className="container mx-auto px-6 py-20 animate-fade-in">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          <div className="lg:w-1/2 text-center lg:text-left animate-slide-up">
            <h1 className="text-5xl lg:text-6xl font-bold text-white mb-6 text-shadow leading-tight">
              Experience Agriculture,
              <span className="text-gradient block mt-2">Harvest the Rewards</span>
            </h1>
            <p className="text-xl text-text-secondary mb-8 leading-relaxed max-w-2xl">
              Connect with real farms and sustainable agriculture. Support expert farmers and enjoy fresh harvests with complete transparency and modern technology.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link to="/auth/login" className="btn-primary text-lg">
                🌱 Start Farming
              </Link>
              <Link to="/auth/register" className="btn-secondary text-lg">
                🚜 Explore Farms
              </Link>
            </div>
          </div>
          <div className="lg:w-1/2 animate-slide-down">
            <div className="bg-glass p-8 rounded-2xl shadow-2xl hover-lift transition-all duration-500">
              <div className="bg-gradient-primary p-6 rounded-xl">
                <img
                  src="/images/farm-tech.svg"
                  alt="AgriTech illustration"
                  className="w-full h-auto animate-float"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="bg-glass py-16 border-y border-border-light">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="bg-card p-6 rounded-xl hover-lift transition-all duration-300">
              <div className="text-5xl font-bold text-gradient mb-3">5K+</div>
              <div className="text-text-secondary font-medium">Community Members</div>
              <div className="text-text-muted text-sm mt-1">Growing daily</div>
            </div>
            <div className="bg-card p-6 rounded-xl hover-lift transition-all duration-300">
              <div className="text-5xl font-bold text-gradient mb-3">25%</div>
              <div className="text-text-secondary font-medium">Yield Increase</div>
              <div className="text-text-muted text-sm mt-1">Average improvement</div>
            </div>
            <div className="bg-card p-6 rounded-xl hover-lift transition-all duration-300">
              <div className="text-5xl font-bold text-gradient mb-3">500+</div>
              <div className="text-text-secondary font-medium">Verified Farmers</div>
              <div className="text-text-muted text-sm mt-1">Expert partners</div>
            </div>
            <div className="bg-card p-6 rounded-xl hover-lift transition-all duration-300">
              <div className="text-5xl font-bold text-gradient mb-3">50K+</div>
              <div className="text-text-secondary font-medium">Crops Delivered</div>
              <div className="text-text-muted text-sm mt-1">Fresh & organic</div>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4 text-shadow">
              How Agricultural Partnership Works
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Simple steps to start your farming journey with expert guidance
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
            <div className="flex flex-col items-center text-center group">
              <div className="bg-gradient-primary w-20 h-20 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-6 shadow-2xl group-hover:scale-110 transition-all duration-300">
                1
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Select Your Plot</h3>
              <p className="text-text-secondary leading-relaxed max-w-sm">
                Browse verified farmland and select the plots you wish to cultivate. Each plot comes with detailed information and expert recommendations.
              </p>
            </div>

            <div className="flex flex-col items-center text-center group">
              <div className="bg-gradient-secondary w-20 h-20 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-6 shadow-2xl group-hover:scale-110 transition-all duration-300">
                2
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Start Cultivation</h3>
              <p className="text-text-secondary leading-relaxed max-w-sm">
                Begin your farming journey with expert guidance and support. Our experienced farmers handle all the technical aspects for you.
              </p>
            </div>

            <div className="flex flex-col items-center text-center group">
              <div className="bg-gradient-primary w-20 h-20 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-6 shadow-2xl group-hover:scale-110 transition-all duration-300">
                3
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Monitor & Harvest</h3>
              <p className="text-text-secondary leading-relaxed max-w-sm">
                Track your crops' progress with real-time updates and expert insights. Enjoy fresh harvests delivered to your door.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-glass py-20 border-y border-border-light">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4 text-shadow">
              Why Join Our Farming Community
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Experience the future of agriculture with modern technology and sustainable practices
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="card p-8 text-center group">
              <div className="bg-gradient-primary w-16 h-16 rounded-2xl flex items-center justify-center text-white text-3xl mb-6 mx-auto group-hover:scale-110 transition-all duration-300">
                🌱
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Zero Experience Needed</h3>
              <p className="text-text-secondary leading-relaxed">
                Expert farmers handle all the technical work while you enjoy fresh harvests. No agricultural knowledge required - just passion for quality food.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="card p-8 text-center group">
              <div className="bg-gradient-secondary w-16 h-16 rounded-2xl flex items-center justify-center text-white text-3xl mb-6 mx-auto group-hover:scale-110 transition-all duration-300">
                🌿
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Sustainable Agriculture</h3>
              <p className="text-text-secondary leading-relaxed">
                Support eco-friendly farming practices that benefit the environment. Every harvest contributes to a more sustainable future.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="card p-8 text-center group">
              <div className="bg-gradient-primary w-16 h-16 rounded-2xl flex items-center justify-center text-white text-3xl mb-6 mx-auto group-hover:scale-110 transition-all duration-300">
                📱
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Complete Transparency</h3>
              <p className="text-text-secondary leading-relaxed">
                Track your crops in real-time with our mobile app. Know exactly where your food comes from with complete farmer profiles and growth updates.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Investment Options */}
      <div className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4 text-shadow">
              Agricultural Cultivation Options
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Choose the farming approach that best fits your goals and lifestyle
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="card p-8 text-center group hover-lift">
              <div className="bg-gradient-primary w-20 h-20 rounded-2xl flex items-center justify-center text-white text-4xl mb-6 mx-auto group-hover:scale-110 transition-all duration-300">
                🌾
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Seasonal Crops</h3>
              <p className="text-text-secondary mb-6 leading-relaxed">
                Cultivate seasonal crops with expert guidance and modern farming techniques. Perfect for beginners.
              </p>
              <div className="bg-gradient-primary text-white px-4 py-2 rounded-full text-lg font-bold mb-6 inline-block">
                3-4 months cycle
              </div>
              <Link to="/crops" className="btn-secondary w-full">
                Browse Crops 🌱
              </Link>
            </div>

            <div className="card p-8 text-center group hover-lift border-2 border-primary relative overflow-hidden">
              <div className="absolute top-0 right-0 bg-gradient-secondary text-white px-4 py-2 rounded-bl-xl text-sm font-bold">
                POPULAR
              </div>
              <div className="bg-gradient-secondary w-20 h-20 rounded-2xl flex items-center justify-center text-white text-4xl mb-6 mx-auto group-hover:scale-110 transition-all duration-300">
                🚜
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Full Farm Management</h3>
              <p className="text-text-secondary mb-6 leading-relaxed">
                Take control of entire farm operations with comprehensive management tools and expert support.
              </p>
              <div className="bg-gradient-secondary text-white px-4 py-2 rounded-full text-lg font-bold mb-6 inline-block">
                Year-round farming
              </div>
              <Link to="/farms" className="btn-primary w-full">
                Explore Farms 🏡
              </Link>
            </div>

            <div className="card p-8 text-center group hover-lift">
              <div className="bg-gradient-primary w-20 h-20 rounded-2xl flex items-center justify-center text-white text-4xl mb-6 mx-auto group-hover:scale-110 transition-all duration-300">
                👥
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Community Farming</h3>
              <p className="text-text-secondary mb-6 leading-relaxed">
                Join a collaborative farming community supported by agricultural experts and fellow farmers.
              </p>
              <div className="bg-gradient-primary text-white px-4 py-2 rounded-full text-lg font-bold mb-6 inline-block">
                Flexible duration
              </div>
              <Link to="/community" className="btn-secondary w-full">
                Join Community 🤝
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials */}
      <div className="bg-glass py-20 border-y border-border-light">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4 text-shadow">
              What Our Community Says
            </h2>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Real stories from farmers and community members who've transformed their relationship with agriculture
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="card p-8 hover-lift">
              <div className="flex items-center mb-6">
                <div className="flex text-secondary text-xl">
                  ⭐⭐⭐⭐⭐
                </div>
              </div>
              <blockquote className="text-text-secondary italic text-lg leading-relaxed mb-6">
                "I've always wanted to connect with where my food comes from. With AgriTech, I can support farmers directly and actually own the produce I receive. The transparency is incredible!"
              </blockquote>
              <div className="flex items-center">
                <div className="w-14 h-14 bg-gradient-primary rounded-full flex items-center justify-center text-white text-xl font-bold mr-4">
                  E
                </div>
                <div>
                  <div className="text-white font-semibold text-lg">Emma Chen</div>
                  <div className="text-text-muted">Urban Investor, New York</div>
                </div>
              </div>
            </div>

            <div className="card p-8 hover-lift">
              <div className="flex items-center mb-6">
                <div className="flex text-secondary text-xl">
                  ⭐⭐⭐⭐⭐
                </div>
              </div>
              <blockquote className="text-text-secondary italic text-lg leading-relaxed mb-6">
                "As someone with no farming experience, I never thought I could participate in agriculture. AgriTech made it easy and transparent. I love watching my crops grow through the app."
              </blockquote>
              <div className="flex items-center">
                <div className="w-14 h-14 bg-gradient-secondary rounded-full flex items-center justify-center text-white text-xl font-bold mr-4">
                  D
                </div>
                <div>
                  <div className="text-white font-semibold text-lg">David Parker</div>
                  <div className="text-text-muted">Tech Professional, San Francisco</div>
                </div>
              </div>
            </div>

            <div className="card p-8 hover-lift">
              <div className="flex items-center mb-6">
                <div className="flex text-secondary text-xl">
                  ⭐⭐⭐⭐⭐
                </div>
              </div>
              <blockquote className="text-text-secondary italic text-lg leading-relaxed mb-6">
                "The satisfaction from my farm participation has been incredible this year, plus I get fresh organic produce. It's a win-win experience that feels amazing."
              </blockquote>
              <div className="flex items-center">
                <div className="w-14 h-14 bg-gradient-primary rounded-full flex items-center justify-center text-white text-xl font-bold mr-4">
                  S
                </div>
                <div>
                  <div className="text-white font-semibold text-lg">Sophia Williams</div>
                  <div className="text-text-muted">Financial Analyst, Chicago</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter */}
      <div className="py-20">
        <div className="container mx-auto px-6">
          <div className="bg-card p-12 rounded-3xl max-w-4xl mx-auto text-center border border-border-strong">
            <div className="mb-8">
              <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4 text-shadow">
                Stay Connected with Agriculture
              </h2>
              <p className="text-xl text-text-secondary max-w-2xl mx-auto leading-relaxed">
                Subscribe to receive updates on new farm opportunities, harvest reports, seasonal insights, and exclusive community benefits
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="input-field flex-grow text-lg"
              />
              <button className="btn-primary text-lg whitespace-nowrap">
                Subscribe 📧
              </button>
            </div>
            <p className="text-text-muted text-sm mt-4">
              Join 5,000+ members already growing with us. Unsubscribe anytime.
            </p>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="container mx-auto px-6 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6 text-shadow leading-tight">
            Become a Farm Partner
            <span className="text-gradient block mt-2">Today</span>
          </h2>
          <p className="text-xl lg:text-2xl text-text-secondary mb-12 max-w-3xl mx-auto leading-relaxed">
            Join thousands of members already participating in and supporting sustainable agriculture. Start your farming journey with expert guidance and modern technology.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link to="/auth/register" className="btn-primary text-xl px-12 py-4">
              🚀 Create Your Account
            </Link>
            <Link to="/auth/login" className="btn-secondary text-xl px-12 py-4">
              🔑 Sign In
            </Link>
          </div>
          <div className="mt-8 flex items-center justify-center gap-8 text-text-muted">
            <div className="flex items-center gap-2">
              <span className="text-primary">✓</span>
              <span>No setup fees</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-primary">✓</span>
              <span>Expert support</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-primary">✓</span>
              <span>Guaranteed harvests</span>
            </div>
          </div>
        </div>
      </div>

    </div>
  )
}