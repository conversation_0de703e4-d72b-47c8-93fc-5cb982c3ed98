import { Product } from './product.types';

// Cart types
export interface Cart {
  id: string;
  userId: string;
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}

export interface CartItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
  addedAt: string;
  updatedAt: string;
}

// Cart request types
export interface AddToCartRequest {
  productId: string;
  quantity: number;
  unit?: string;
  notes?: string;
}

export interface UpdateCartItemRequest {
  quantity: number;
  notes?: string;
}

export interface RemoveFromCartRequest {
  itemId: string;
}

// Cart summary
export interface CartSummary {
  totalItems: number;
  totalAmount: number;
  currency: string;
  itemCount: number;
  savings?: number;
  taxes?: number;
  shippingCost?: number;
  finalAmount: number;
}

// Bulk cart operations
export interface BulkCartOperation {
  action: 'add' | 'update' | 'remove';
  items: BulkCartItem[];
}

export interface BulkCartItem {
  productId: string;
  quantity?: number;
  unit?: string;
  notes?: string;
}
