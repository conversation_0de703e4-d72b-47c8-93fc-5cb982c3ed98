// Global theme colors and variables
const theme = {
  colors: {
    // Primary colors
    primary: {
      main: '#5D3891', // Dark purple
      light: '#8458B3', // Lighter purple
      dark: '#3E2560', // Darker purple
      contrastText: '#FFFFFF', // White text for primary backgrounds
    },
    // Secondary colors
    secondary: {
      main: '#D0BFFF', // Light lavender
      light: '#E5D9FF', // Very light lavender
      dark: '#A084DC', // Darker lavender
      contrastText: '#333333', // Dark text for secondary backgrounds
    },
    // Background colors
    background: {
      default: '#F8F6FF', // Very light purple tint for general backgrounds
      paper: '#FFFFFF', // White for cards and panels
      dark: '#303030', // Dark background for footer etc.
    },
    // Text colors
    text: {
      primary: '#333333', // Near black for primary text
      secondary: '#666666', // Dark gray for secondary text
      disabled: '#999999', // Medium gray for disabled text
      hint: '#888888', // For hint text
    },
    // Status colors
    status: {
      success: '#4CAF50', // Green
      warning: '#FFC107', // Amber
      error: '#F44336', // Red
      info: '#2196F3', // Blue
    },
    // Border and divider colors
    border: {
      light: '#EEEEEE', // Very light gray
      main: '#E0E0E0', // Light gray
      dark: '#BDBDBD', // Medium gray
    }
  },
  // Spacing units
  spacing: {
    xs: '0.25rem', // 4px
    sm: '0.5rem',  // 8px
    md: '1rem',    // 16px
    lg: '1.5rem',  // 24px
    xl: '2rem',    // 32px
    xxl: '3rem',   // 48px
  },
  // Border radius
  borderRadius: {
    sm: '0.125rem', // 2px
    md: '0.25rem',  // 4px
    lg: '0.5rem',   // 8px
    xl: '1rem',     // 16px
    full: '9999px', // For circular elements
  },
  // Shadow styles
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },
  // Font sizes
  typography: {
    xs: '0.75rem',  // 12px
    sm: '0.875rem', // 14px
    md: '1rem',     // 16px
    lg: '1.125rem', // 18px
    xl: '1.25rem',  // 20px
    xxl: '1.5rem',  // 24px
    h1: '2.5rem',   // 40px
    h2: '2rem',     // 32px
    h3: '1.75rem',  // 28px 
    h4: '1.5rem',   // 24px
    h5: '1.25rem',  // 20px
    h6: '1rem',     // 16px
  },
  // Font weights
  fontWeights: {
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  }
};

export default theme; 