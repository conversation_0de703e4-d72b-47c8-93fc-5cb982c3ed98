import { AuthService } from './auth.service';
import { ProductService } from './product.service';
import { ProfileService } from './profile.service';
import { OrderService } from './order.service';
import { CartService } from './cart.service';
import { getEnvironment } from '../helpers/environment';

class ServiceFactory {
  private static instance: ServiceFactory;
  private authService: AuthService | null = null;
  private productService: ProductService | null = null;
  private profileService: ProfileService | null = null;
  private orderService: OrderService | null = null;
  private cartService: CartService | null = null;

  private constructor() {}

  static getInstance(): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory();
    }
    return ServiceFactory.instance;
  }

  getAuthService(): AuthService {
    if (!this.authService) {
      const env = getEnvironment();
      this.authService = new AuthService(env.AUTH_API_URL);
    }
    return this.authService;
  }

  getProductService(): ProductService {
    if (!this.productService) {
      const env = getEnvironment();
      this.productService = new ProductService(env.PRODUCT_API_URL);
    }
    return this.productService;
  }

  getProfileService(): ProfileService {
    if (!this.profileService) {
      const env = getEnvironment();
      this.profileService = new ProfileService(env.PROFILE_API_URL);
    }
    return this.profileService;
  }

  getOrderService(): OrderService {
    if (!this.orderService) {
      const env = getEnvironment();
      this.orderService = new OrderService(env.ORDER_API_URL);
    }
    return this.orderService;
  }

  getCartService(): CartService {
    if (!this.cartService) {
      const env = getEnvironment();
      this.cartService = new CartService(env.CART_API_URL);
    }
    return this.cartService;
  }

  // Method to reset all services (useful for testing or environment changes)
  resetServices(): void {
    this.authService = null;
    this.productService = null;
    this.profileService = null;
    this.orderService = null;
    this.cartService = null;
  }
}

// Export singleton instance methods
export const getAuthService = () => ServiceFactory.getInstance().getAuthService();
export const getProductService = () => ServiceFactory.getInstance().getProductService();
export const getProfileService = () => ServiceFactory.getInstance().getProfileService();
export const getOrderService = () => ServiceFactory.getInstance().getOrderService();
export const getCartService = () => ServiceFactory.getInstance().getCartService();

export default ServiceFactory;
