import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'

// Define crop data types
interface CropDetail {
  id: string;
  name: string;
  type: string;
  imageUrl: string;
  location: string;
  description: string;
  price: number;
  stockStatus: 'In Stock' | 'Low Stock' | 'Out of Stock';
  season: string;
  certification: string;
  rating: number;
  reviews: number;
  farmDetails: {
    farmName: string;
    farmerId: string;
    farmSize: string;
    farmingPractice: string;
    soilType: string;
    irrigation: string;
    location: string;
  };
  growthTimeline: {
    stage: string;
    date: string;
    completed: boolean;
  }[];
  nutritionalInfo: {
    calories: number;
    protein: string;
    carbs: string;
    fiber: string;
    vitamins: string[];
  };
  uses: string[];
  growingGuide: {
    soilPreparation: string;
    planting: string;
    careInstructions: string;
    harvesting: string;
  };
}

// Mock data for the crop details
const mockCropDetail: CropDetail = {
  id: 'crop3',
  name: 'Premium Tomato Seeds',
  type: 'Vegetable',
  imageUrl: 'https://images.unsplash.com/photo-1546094096-0df4bcaaa337?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
  location: 'Karnataka, India',
  description: 'Premium heirloom tomato seed selection for vibrant, flavorful produce. These Roma and Cherry varieties are known for their excellent disease resistance and high yield in various growing conditions.',
  price: 89.99,
  stockStatus: 'Low Stock',
  season: 'Spring',
  certification: 'Organic',
  rating: 4.9,
  reviews: 156,
  farmDetails: {
    farmName: 'Green Acres Organic Farm',
    farmerId: 'farmer123',
    farmSize: '25 acres',
    farmingPractice: 'Organic',
    soilType: 'Rich loamy soil with high organic content',
    irrigation: 'Drip irrigation with rainwater harvesting',
    location: 'Karnataka, India'
  },
  growthTimeline: [
    {
      stage: 'Planting',
      date: 'Feb 15, 2024',
      completed: true
    },
    {
      stage: 'Seedling growth',
      date: 'March 1, 2024',
      completed: true
    },
    {
      stage: 'Flowering',
      date: 'April 10, 2024',
      completed: false
    },
    {
      stage: 'Fruit development',
      date: 'May 5, 2024',
      completed: false
    },
    {
      stage: 'Harvesting',
      date: 'June 20, 2024',
      completed: false
    }
  ],
  nutritionalInfo: {
    calories: 18,
    protein: '0.9g',
    carbs: '3.9g',
    fiber: '1.2g',
    vitamins: ['Vitamin C', 'Vitamin K', 'Potassium', 'Folate', 'Lycopene']
  },
  uses: [
    'Fresh consumption in salads',
    'Cooking in sauces, soups, and stews',
    'Canning and preserving',
    'Juicing'
  ],
  growingGuide: {
    soilPreparation: 'Prepare soil with organic compost and ensure good drainage.',
    planting: 'Plant seeds 1/4 inch deep, spaced 18-24 inches apart.',
    careInstructions: 'Water regularly, provide support for vines, and prune as needed.',
    harvesting: 'Harvest when fruits are firm and fully colored.'
  }
};

export default function CropDetails() {
  const { cropId } = useParams<{ cropId: string }>();
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('overview');
  
  // In a real application, fetch data based on cropId
  const crop = mockCropDetail;
  const totalPrice = (crop.price * quantity).toFixed(2);
  
  return (
    <div className="bg-bg-default min-h-screen pb-12">
      {/* Hero Banner with crop image */}
      <div className="relative h-[300px] overflow-hidden">
        <img 
          src={crop.imageUrl} 
          alt={crop.name} 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end">
          <div className="container mx-auto px-6 py-8">
            <div className="flex items-center space-x-2 text-white text-sm mb-2">
              <Link to="/crop-marketplace" className="hover:text-primary-light">Crop Marketplace</Link>
              <span>›</span>
              <span>{crop.type}</span>
            </div>
            <h1 className="text-4xl font-bold text-white">{crop.name}</h1>
            <div className="flex items-center mt-2">
              <span className="text-gray-300">{crop.location}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left column - Crop details */}
          <div className="flex-1">
            <div className="bg-bg-paper rounded-lg overflow-hidden shadow-lg mb-8">
              {/* Tabs navigation */}
              <div className="flex border-b border-bg-elevated">
                <button 
                  onClick={() => setActiveTab('overview')}
                  className={`px-6 py-4 font-medium ${activeTab === 'overview' ? 'text-primary border-b-2 border-primary' : 'text-gray-400'}`}
                >
                  Crop Overview
                </button>
                <button 
                  onClick={() => setActiveTab('growing')}
                  className={`px-6 py-4 font-medium ${activeTab === 'growing' ? 'text-primary border-b-2 border-primary' : 'text-gray-400'}`}
                >
                  Growing Guide
                </button>
                <button 
                  onClick={() => setActiveTab('nutrition')}
                  className={`px-6 py-4 font-medium ${activeTab === 'nutrition' ? 'text-primary border-b-2 border-primary' : 'text-gray-400'}`}
                >
                  Nutrition
                </button>
                <button 
                  onClick={() => setActiveTab('farm')}
                  className={`px-6 py-4 font-medium ${activeTab === 'farm' ? 'text-primary border-b-2 border-primary' : 'text-gray-400'}`}
                >
                  Farm Details
                </button>
              </div>

              {/* Tab content */}
              <div className="p-6">
                {/* Crop Overview tab */}
                {activeTab === 'overview' && (
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-4">About {crop.name}</h2>
                    <p className="text-gray-300 mb-6">{crop.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">🌱</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Type</p>
                          <p className="text-white font-medium">{crop.type}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">🍂</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Season</p>
                          <p className="text-white font-medium">{crop.season}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">✓</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Certification</p>
                          <p className="text-white font-medium">{crop.certification}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">⭐</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Rating</p>
                          <p className="text-white font-medium">{crop.rating} ({crop.reviews} reviews)</p>
                        </div>
                      </div>
                    </div>

                    <h3 className="text-xl font-bold text-white mb-4">Growth Timeline</h3>
                    <div className="space-y-6 mb-8">
                      {crop.growthTimeline.map((stage, index) => (
                        <div key={index} className="flex">
                          <div className="mr-4 relative">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${stage.completed ? 'bg-primary' : 'bg-bg-elevated'}`}>
                              {stage.completed ? '✓' : '○'}
                            </div>
                            {index < crop.growthTimeline.length - 1 && (
                              <div className={`absolute top-8 left-4 w-0.5 h-12 ${stage.completed ? 'bg-primary' : 'bg-bg-elevated'}`}></div>
                            )}
                          </div>
                          <div className="pb-6">
                            <h4 className="text-white font-medium">{stage.stage}</h4>
                            <p className="text-sm text-gray-400">Expected: {stage.date}</p>
                            {stage.completed && (
                              <span className="inline-block mt-1 text-xs bg-primary/20 text-primary-light px-2 py-0.5 rounded">
                                Completed
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>

                    <h3 className="text-xl font-bold text-white mb-4">Uses</h3>
                    <ul className="list-disc pl-5 mb-6 text-gray-300 space-y-1">
                      {crop.uses.map((use, index) => (
                        <li key={index}>{use}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Growing Guide tab */}
                {activeTab === 'growing' && (
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-4">Growing Guide</h2>
                    
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-xl font-medium text-white mb-2">Soil Preparation</h3>
                        <p className="text-gray-300">{crop.growingGuide.soilPreparation}</p>
                      </div>
                      
                      <div>
                        <h3 className="text-xl font-medium text-white mb-2">Planting</h3>
                        <p className="text-gray-300">{crop.growingGuide.planting}</p>
                      </div>
                      
                      <div>
                        <h3 className="text-xl font-medium text-white mb-2">Care Instructions</h3>
                        <p className="text-gray-300">{crop.growingGuide.careInstructions}</p>
                      </div>
                      
                      <div>
                        <h3 className="text-xl font-medium text-white mb-2">Harvesting</h3>
                        <p className="text-gray-300">{crop.growingGuide.harvesting}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Nutrition tab */}
                {activeTab === 'nutrition' && (
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-4">Nutritional Information</h2>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="bg-bg-elevated p-4 rounded-lg">
                        <p className="text-sm text-gray-400">Calories</p>
                        <p className="text-xl font-bold text-white">{crop.nutritionalInfo.calories}</p>
                      </div>
                      
                      <div className="bg-bg-elevated p-4 rounded-lg">
                        <p className="text-sm text-gray-400">Protein</p>
                        <p className="text-xl font-bold text-white">{crop.nutritionalInfo.protein}</p>
                      </div>
                      
                      <div className="bg-bg-elevated p-4 rounded-lg">
                        <p className="text-sm text-gray-400">Carbs</p>
                        <p className="text-xl font-bold text-white">{crop.nutritionalInfo.carbs}</p>
                      </div>
                      
                      <div className="bg-bg-elevated p-4 rounded-lg">
                        <p className="text-sm text-gray-400">Fiber</p>
                        <p className="text-xl font-bold text-white">{crop.nutritionalInfo.fiber}</p>
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-white mb-3">Vitamins & Minerals</h3>
                    <div className="flex flex-wrap gap-2 mb-6">
                      {crop.nutritionalInfo.vitamins.map((vitamin, index) => (
                        <span 
                          key={index}
                          className="bg-primary/20 text-primary-light px-3 py-1 rounded-full text-sm"
                        >
                          {vitamin}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Farm Details tab */}
                {activeTab === 'farm' && (
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-4">Farm Details</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">🏡</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Farm Name</p>
                          <p className="text-white font-medium">{crop.farmDetails.farmName}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">📏</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Farm Size</p>
                          <p className="text-white font-medium">{crop.farmDetails.farmSize}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">🌾</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Farming Practice</p>
                          <p className="text-white font-medium">{crop.farmDetails.farmingPractice}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">🌱</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Soil Type</p>
                          <p className="text-white font-medium">{crop.farmDetails.soilType}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">💧</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Irrigation</p>
                          <p className="text-white font-medium">{crop.farmDetails.irrigation}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-primary-light/20 flex items-center justify-center">
                          <i className="text-primary-light">📍</i>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Location</p>
                          <p className="text-white font-medium">{crop.farmDetails.location}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right column - Purchase section */}
          <div className="lg:w-80">
            <div className="bg-bg-paper rounded-lg shadow-lg p-6 sticky top-24">
              <h2 className="text-xl font-bold text-white mb-4">Start Cultivating This Crop</h2>
              
              <div className="border-t border-bg-elevated pt-4 mb-6">
                <div className="flex justify-between mb-2">
                  <span className="text-gray-400">Plot Size</span>
                  <span className="text-white">{totalPrice} acres</span>
                </div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-400">Growing Season</span>
                  <span className="text-primary-light">3-4 months</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Cultivation Period</span>
                  <span className="text-white">March - June</span>
                </div>
              </div>
              
              <button className="w-full bg-[#F97316] hover:bg-[#F97316]/90 text-white mb-3 py-2 rounded-full">
                Reserve Plot
              </button>
              
              <button className="w-full border border-[#22C55E] text-[#22C55E] hover:bg-[#22C55E]/10 py-2 rounded-full">
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 