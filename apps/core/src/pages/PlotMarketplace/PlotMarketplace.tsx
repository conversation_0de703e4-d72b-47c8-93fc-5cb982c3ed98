import React, { useState } from 'react'
import { Link } from 'react-router-dom'

// Mock data for demonstration purposes
const MOCK_PLOTS = [
  {
    id: 1,
    name: 'Riverside Plot',
    location: 'Riverdale County',
    size: '2.5 acres',
    price: 1200,
    soilQuality: 'High',
    waterSource: 'River irrigation',
    organicCertified: true,
    farmer: {
      id: 101,
      name: '<PERSON>',
      experience: '15 years',
      rating: 4.8
    },
    image: '/assets/plot1.jpg',
    description: 'Rich riverside plot perfect for vegetable farming with excellent water access.',
    imageUrl: '/assets/plot1.jpg',
    growingSeason: 'Spring to Fall',
    suitableCrops: 'Tomatoes, Lettuce, Cucumbers'
  },
  {
    id: 2,
    name: 'Hilltop Vineyard',
    location: 'Sunnydale Hills',
    size: '5 acres',
    price: 2500,
    soilQuality: 'Medium',
    waterSource: 'Well and rainwater collection',
    organicCertified: false,
    farmer: {
      id: 102,
      name: '<PERSON>',
      experience: '20 years',
      rating: 4.9
    },
    image: '/assets/plot2.jpg',
    description: 'Sloped terrain ideal for vineyards with excellent sun exposure throughout the day.',
    imageUrl: '/assets/plot2.jpg',
    growingSeason: 'Spring to Fall',
    suitableCrops: 'Grapes, Chardonnay, Pinot Noir'
  },
  {
    id: 3,
    name: 'Valley Orchard',
    location: 'Green Valley',
    size: '3.8 acres',
    price: 1800,
    soilQuality: 'High',
    waterSource: 'Natural spring',
    organicCertified: true,
    farmer: {
      id: 103,
      name: 'David Williams',
      experience: '12 years',
      rating: 4.6
    },
    image: '/assets/plot3.jpg',
    description: 'Protected valley location perfect for fruit trees with rich soil and natural water source.',
    imageUrl: '/assets/plot3.jpg',
    growingSeason: 'Spring to Fall',
    suitableCrops: 'Apples, Peaches, Pears'
  },
  {
    id: 4,
    name: 'Prairie Field',
    location: 'Eastland County',
    size: '10 acres',
    price: 3500,
    soilQuality: 'Medium',
    waterSource: 'Irrigation system',
    organicCertified: false,
    farmer: {
      id: 104,
      name: 'Sarah Johnson',
      experience: '8 years',
      rating: 4.5
    },
    image: '/assets/plot4.jpg',
    description: 'Large open field ideal for grain crops with modern irrigation infrastructure.',
    imageUrl: '/assets/plot4.jpg',
    growingSeason: 'Spring to Fall',
    suitableCrops: 'Wheat, Corn, Barley'
  }
]

export default function PlotMarketplace() {
  const [activeView, setActiveView] = useState('grid') // grid or map
  const [filters, setFilters] = useState({
    location: '',
    minSize: '',
    maxPrice: '',
    soilQuality: '',
    organicOnly: false
  })

  // Handle filter changes
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement
    
    setFilters({
      ...filters,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    })
  }

  // Filter plots based on criteria
  const filteredPlots = MOCK_PLOTS.filter(plot => {
    const matchesLocation = !filters.location || plot.location.toLowerCase().includes(filters.location.toLowerCase())
    const matchesSize = !filters.minSize || parseFloat(plot.size) >= parseFloat(filters.minSize)
    const matchesPrice = !filters.maxPrice || plot.price <= parseInt(filters.maxPrice)
    const matchesSoil = !filters.soilQuality || plot.soilQuality === filters.soilQuality
    const matchesOrganic = !filters.organicOnly || plot.organicCertified
    
    return matchesLocation && matchesSize && matchesPrice && matchesSoil && matchesOrganic
  })

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Agricultural Plots</h1>
          <p className="text-gray-600">Find the perfect land to start your farming journey</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <button
            onClick={() => setActiveView('grid')}
            className={`px-4 py-2 rounded ${activeView === 'grid' 
              ? 'bg-green-600 text-white' 
              : 'bg-gray-200 text-gray-700'}`}
            aria-label="View as grid"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
            Grid
          </button>
          <button
            onClick={() => setActiveView('map')}
            className={`px-4 py-2 rounded ${activeView === 'map' 
              ? 'bg-green-600 text-white' 
              : 'bg-gray-200 text-gray-700'}`}
            aria-label="View as map"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l2.293 2.293A1 1 0 0018 16V6a1 1 0 00-.293-.707z" clipRule="evenodd" />
            </svg>
            Map
          </button>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row">
        {/* Filters sidebar */}
        <div className="lg:w-1/4 mb-6 lg:mb-0 lg:pr-6">
          <div className="bg-white p-4 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Filter Plots</h2>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
              <input
                type="text"
                name="location"
                value={filters.location}
                onChange={handleFilterChange}
                placeholder="Search by location"
                className="w-full p-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Minimum Size (acres)</label>
              <input
                type="number"
                name="minSize"
                value={filters.minSize}
                onChange={handleFilterChange}
                placeholder="0"
                className="w-full p-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Maximum Price ($)</label>
              <input
                type="number"
                name="maxPrice"
                value={filters.maxPrice}
                onChange={handleFilterChange}
                placeholder="Any"
                className="w-full p-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Soil Quality</label>
              <select
                name="soilQuality"
                value={filters.soilQuality}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
              >
                <option value="">Any</option>
                <option value="High">High</option>
                <option value="Medium">Medium</option>
                <option value="Low">Low</option>
              </select>
            </div>
            
            <div className="mb-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="organicOnly"
                  checked={filters.organicOnly}
                  onChange={handleFilterChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Organic Certified Only</span>
              </label>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-md mt-4">
            <h2 className="text-xl font-semibold mb-4">Popular Farmers</h2>
            <ul className="space-y-3">
              {MOCK_PLOTS.map(plot => (
                <li key={`farmer-${plot.farmer.id}`} className="flex items-center">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-green-800 font-semibold mr-3">
                    {plot.farmer.name.charAt(0)}
                  </div>
                  <div>
                    <p className="font-medium">{plot.farmer.name}</p>
                    <p className="text-sm text-gray-600">Rating: {plot.farmer.rating}/5</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Main content area */}
        <div className="lg:w-3/4">
          {activeView === 'map' ? (
            <div className="bg-white p-4 rounded-lg shadow-md h-96 flex items-center justify-center">
              <div className="text-center">
                <div className="bg-gray-200 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                  </svg>
                </div>
                <p className="text-gray-600">Interactive map would be displayed here</p>
                <p className="text-sm text-gray-500">Showing {filteredPlots.length} available plots</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPlots.map(plot => (
                <div key={plot.id} className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
                  <img 
                    src={plot.imageUrl} 
                    alt={plot.name} 
                    className="w-full h-48 object-cover"
                  />
                  
                  <div className="p-4 flex-grow">
                    <div className="flex justify-between items-start">
                      <h3 className="text-xl font-semibold mb-2">{plot.name}</h3>
                      <span className="text-lg font-bold text-[#22C55E]">{plot.size} acres</span>
                    </div>
                    <p className="text-gray-600 mb-2">{plot.location}</p>
                    <p className="text-gray-700 mb-3">{plot.description}</p>
                    
                    <div className="grid grid-cols-2 gap-2 mb-4">
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Soil Type:</span> {plot.soilQuality}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Water Source:</span> {plot.waterSource}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Growing Season:</span> {plot.growingSeason}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Suitable Crops:</span> {plot.suitableCrops}
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 border-t border-gray-200">
                    <div className="flex justify-between">
                      <button className="border border-[#22C55E] text-[#22C55E] px-4 py-2 rounded-full hover:bg-[#22C55E]/10">
                        View Details
                      </button>
                      <button className="bg-[#F97316] text-white px-4 py-2 rounded-full hover:bg-[#F97316]/90">
                        Start Cultivation
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {filteredPlots.length === 0 && (
            <div className="bg-white p-8 rounded-lg shadow-md text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No plots found</h3>
              <p className="text-gray-600 mb-4">Try adjusting your filters to find more results.</p>
              <button 
                onClick={() => setFilters({
                  location: '',
                  minSize: '',
                  maxPrice: '',
                  soilQuality: '',
                  organicOnly: false
                })}
                className="text-green-700 font-medium hover:text-green-800"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 