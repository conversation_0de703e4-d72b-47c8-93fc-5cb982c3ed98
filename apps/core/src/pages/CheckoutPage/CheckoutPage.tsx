import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'

// Mock data for checkout demonstration
const CHECKOUT_DATA = {
  plot: {
    id: 1,
    name: 'Riverside Plot',
    location: 'Riverdale County',
    size: '2.5 acres',
    image: '/assets/plot1.jpg',
    price: 1200
  },
  farmer: {
    id: 101,
    name: '<PERSON>',
    rating: 4.8
  },
  ownershipOptions: [
    { id: 1, name: '3 Month Share', price: 350, description: 'Short-term seasonal plot ownership' },
    { id: 2, name: '6 Month Share', price: 650, description: 'Half-year plot ownership with priority crop selection' },
    { id: 3, name: '12 Month Share', price: 1200, description: 'Full year plot ownership with complete benefits' }
  ]
}

export default function CheckoutPage() {
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedOption, setSelectedOption] = useState(CHECKOUT_DATA.ownershipOptions[1])
  const [contactPreference, setContactPreference] = useState('email')
  const [agreeTerms, setAgreeTerms] = useState(false)
  
  // Handle next step in checkout process
  const handleNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    } else {
      // Submit order
      navigate('/profile')
    }
  }
  
  // Handle previous step in checkout process
  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-5xl">
      {/* Checkout Progress */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-6">Complete Your Plot Ownership</h1>
        <div className="flex items-center justify-between mb-8">
          <div className="flex-1">
            <div className={`h-2 ${currentStep >= 1 ? 'bg-green-600' : 'bg-gray-200'} rounded-l-full`}></div>
          </div>
          <div className="flex items-center justify-center relative mx-4">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${currentStep >= 1 ? 'bg-green-600 text-white' : 'bg-gray-200'} z-10`}>
              1
            </div>
            <span className="absolute -bottom-6 text-xs font-medium">Plot Details</span>
          </div>
          <div className="flex-1">
            <div className={`h-2 ${currentStep >= 2 ? 'bg-green-600' : 'bg-gray-200'}`}></div>
          </div>
          <div className="flex items-center justify-center relative mx-4">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${currentStep >= 2 ? 'bg-green-600 text-white' : 'bg-gray-200'} z-10`}>
              2
            </div>
            <span className="absolute -bottom-6 text-xs font-medium">Ownership Terms</span>
          </div>
          <div className="flex-1">
            <div className={`h-2 ${currentStep >= 3 ? 'bg-green-600' : 'bg-gray-200'}`}></div>
          </div>
          <div className="flex items-center justify-center relative mx-4">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${currentStep >= 3 ? 'bg-green-600 text-white' : 'bg-gray-200'} z-10`}>
              3
            </div>
            <span className="absolute -bottom-6 text-xs font-medium">Payment</span>
          </div>
          <div className="flex-1">
            <div className={`h-2 ${currentStep >= 3 ? 'bg-green-600' : 'bg-gray-200'} rounded-r-full`}></div>
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Main Checkout Form */}
        <div className="lg:w-2/3">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            {/* Step 1: Plot Details */}
            {currentStep === 1 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Confirm Plot Details</h2>
                <div className="flex flex-col md:flex-row mb-6">
                  <div className="md:w-1/3 mb-4 md:mb-0 md:mr-6">
                    <div className="bg-gray-200 rounded-lg overflow-hidden">
                      <img 
                        src={CHECKOUT_DATA.plot.image} 
                        alt={CHECKOUT_DATA.plot.name} 
                        className="w-full h-48 object-cover"
                      />
                    </div>
                  </div>
                  <div className="md:w-2/3">
                    <h3 className="text-lg font-semibold mb-2">{CHECKOUT_DATA.plot.name}</h3>
                    <p className="text-gray-600 mb-3">{CHECKOUT_DATA.plot.location}</p>
                    <p className="mb-3">Size: <span className="font-medium">{CHECKOUT_DATA.plot.size}</span></p>
                    <p className="mb-3">Farmer: <span className="font-medium">{CHECKOUT_DATA.farmer.name}</span></p>
                    <div className="flex items-center mb-4">
                      <span className="text-sm text-gray-600 mr-2">Farmer Rating:</span>
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <svg key={i} xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${i < Math.floor(CHECKOUT_DATA.farmer.rating) ? 'text-yellow-400' : 'text-gray-300'}`} viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-sm text-gray-600 ml-1">{CHECKOUT_DATA.farmer.rating}</span>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-4">Ownership Description</h3>
                  <p className="text-gray-700 mb-4">
                    By purchasing a share in this plot, you will become a temporary part-owner during 
                    the specified period. You'll receive regular updates about your plot, including 
                    photos and progress reports. At harvest time, you'll receive a portion of the 
                    produced crops based on your ownership share.
                  </p>
                </div>
              </div>
            )}

            {/* Step 2: Ownership Terms */}
            {currentStep === 2 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Select Ownership Option</h2>
                <div className="space-y-4 mb-6">
                  {CHECKOUT_DATA.ownershipOptions.map(option => (
                    <div 
                      key={option.id}
                      className={`border rounded-lg p-4 cursor-pointer transition ${selectedOption.id === option.id ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-gray-300'}`}
                      onClick={() => setSelectedOption(option)}
                    >
                      <div className="flex items-center">
                        <div className={`w-5 h-5 rounded-full border flex-shrink-0 mr-3 ${selectedOption.id === option.id ? 'border-green-500 bg-green-500' : 'border-gray-400'}`}>
                          {selectedOption.id === option.id && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <div className="flex-grow">
                          <div className="flex justify-between items-center mb-1">
                            <h3 className="font-medium">{option.name}</h3>
                            <span className="font-bold text-green-700">${option.price}</span>
                          </div>
                          <p className="text-gray-600 text-sm">{option.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-4">Communication Preferences</h3>
                  <p className="text-gray-600 mb-4">How would you like to receive updates about your plot?</p>
                  
                  <div className="flex flex-col space-y-3">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="contactPreference"
                        value="email"
                        checked={contactPreference === 'email'}
                        onChange={() => setContactPreference('email')}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <span className="ml-2 text-gray-700">Email Updates (Weekly)</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="contactPreference"
                        value="sms"
                        checked={contactPreference === 'sms'}
                        onChange={() => setContactPreference('sms')}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <span className="ml-2 text-gray-700">SMS Updates (Weekly)</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="contactPreference"
                        value="both"
                        checked={contactPreference === 'both'}
                        onChange={() => setContactPreference('both')}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <span className="ml-2 text-gray-700">Both Email and SMS</span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Payment Information */}
            {currentStep === 3 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Payment Information</h2>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Card Number</label>
                  <input 
                    type="text" 
                    placeholder="1234 5678 9012 3456" 
                    className="w-full p-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Expiration Date</label>
                    <input 
                      type="text" 
                      placeholder="MM/YY" 
                      className="w-full p-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Security Code</label>
                    <input 
                      type="text" 
                      placeholder="123" 
                      className="w-full p-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                    />
                  </div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Cardholder Name</label>
                  <input 
                    type="text" 
                    placeholder="John Doe" 
                    className="w-full p-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={agreeTerms}
                      onChange={() => setAgreeTerms(!agreeTerms)}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      I agree to the <a href="#" className="text-green-600 hover:text-green-700">terms and conditions</a> of 
                      plot ownership, including the shared responsibility and terms of use.
                    </span>
                  </label>
                </div>
              </div>
            )}
          </div>
          
          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <button
              onClick={handlePreviousStep}
              className={`px-4 py-2 border border-gray-300 rounded-md text-gray-700 ${currentStep === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}`}
              disabled={currentStep === 1}
            >
              Back
            </button>
            <button
              onClick={handleNextStep}
              disabled={currentStep === 3 && !agreeTerms}
              className={`px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${currentStep === 3 && !agreeTerms ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {currentStep < 3 ? 'Continue' : 'Complete Purchase'}
            </button>
          </div>
        </div>
        
        {/* Order Summary */}
        <div className="lg:w-1/3">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-6">
            <h2 className="text-lg font-semibold mb-4">Order Summary</h2>
            
            <div className="border-t border-gray-200 pt-4 mb-4">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Plot</span>
                <span className="font-medium">{CHECKOUT_DATA.plot.name}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Location</span>
                <span>{CHECKOUT_DATA.plot.location}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Size</span>
                <span>{CHECKOUT_DATA.plot.size}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Ownership</span>
                <span>{selectedOption.name}</span>
              </div>
            </div>
            
            <div className="border-t border-gray-200 pt-4 mb-4">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Subtotal</span>
                <span>${selectedOption.price}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Platform Fee</span>
                <span>${Math.round(selectedOption.price * 0.05)}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Tax</span>
                <span>${Math.round(selectedOption.price * 0.08)}</span>
              </div>
            </div>
            
            <div className="border-t border-gray-200 pt-4">
              <div className="flex justify-between">
                <span className="font-semibold">Total</span>
                <span className="font-bold text-lg text-green-700">
                  ${selectedOption.price + Math.round(selectedOption.price * 0.05) + Math.round(selectedOption.price * 0.08)}
                </span>
              </div>
            </div>
            
            <div className="mt-6 bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm text-gray-700">Secure payment</span>
              </div>
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                  <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-1H3V5h11v5h1a1 1 0 001-1V5a1 1 0 00-1-1H3zM17 15.5a.5.5 0 01-.5.5h-2a.5.5 0 010-1h2a.5.5 0 01.5.5z" />
                </svg>
                <span className="text-sm text-gray-700">Regular plot updates</span>
              </div>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clipRule="evenodd" />
                </svg>
                <span className="text-sm text-gray-700">Harvest share delivery</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
