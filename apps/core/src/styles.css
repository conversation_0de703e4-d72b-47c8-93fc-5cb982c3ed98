@tailwind base;
@tailwind components;
@tailwind utilities;
/* You can add global styles to this file, and also import other style files */

:root {
  /* Primary colors - Green theme for agriculture */
  --color-primary: #22C55E;
  --color-primary-light: #34D399;
  --color-primary-dark: #15803D;
  --color-primary-contrast: #FFFFFF;

  /* Secondary colors - Orange accent */
  --color-secondary: #F97316;
  --color-secondary-light: #FB923C;
  --color-secondary-dark: #EA580C;
  --color-secondary-contrast: #FFFFFF;

  /* Background colors - Modern gradient approach */
  --color-bg-default: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
  --color-bg-paper: #FFFFFF;
  --color-bg-elevated: rgba(255, 255, 255, 0.05);
  --color-bg-card: rgba(255, 255, 255, 0.08);
  --color-bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text colors - High contrast for readability */
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #CBD5E1;
  --color-text-muted: #94A3B8;
  --color-text-disabled: #64748B;
  --color-text-dark: #1E293B;

  /* Status colors */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;

  /* Border colors */
  --color-border: rgba(255, 255, 255, 0.1);
  --color-border-light: rgba(255, 255, 255, 0.05);
  --color-border-strong: rgba(255, 255, 255, 0.2);
}

body {
  background: var(--color-bg-default);
  color: var(--color-text-primary);
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  min-height: 100vh;
}

/* Custom utility classes */
.btn-primary {
  @apply px-6 py-3 rounded-lg font-semibold text-center inline-flex items-center justify-center;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-primary-contrast);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  text-decoration: none;
  box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(34, 197, 94, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px 0 rgba(34, 197, 94, 0.3);
}

.btn-secondary {
  @apply px-6 py-3 rounded-lg font-semibold text-center inline-flex items-center justify-center;
  background: transparent;
  border: 2px solid var(--color-secondary);
  color: var(--color-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  text-decoration: none;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--color-secondary);
  color: var(--color-secondary-contrast);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(249, 115, 22, 0.4);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px 0 rgba(249, 115, 22, 0.3);
}

/* Text utilities */
.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.text-muted {
  color: var(--color-text-muted);
}

.text-gradient {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Background utilities */
.bg-primary {
  background-color: var(--color-primary);
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.bg-glass {
  background: var(--color-bg-elevated);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
}

.bg-card {
  background: var(--color-bg-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
}

/* Border utilities */
.border-primary {
  border-color: var(--color-primary);
}

.border-glass {
  border-color: var(--color-border);
}

/* Hover effects */
.hover-bg-primary:hover {
  background-color: var(--color-primary-light);
}

.hover-text-primary:hover {
  color: var(--color-primary-light);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

/* Card styles */
.card {
  @apply rounded-xl overflow-hidden;
  background: var(--color-bg-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border-color: var(--color-border-strong);
}

/* Input styles */
.input-field {
  @apply px-4 py-3 rounded-lg border w-full;
  background: var(--color-bg-elevated);
  backdrop-filter: blur(20px);
  border-color: var(--color-border);
  color: var(--color-text-primary);
  transition: all 0.3s ease;
}

.input-field:focus {
  @apply outline-none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
  background: var(--color-bg-card);
}

.input-field::placeholder {
  color: var(--color-text-muted);
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
}

.bg-gradient-hero {
  background: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
}

/* Typography improvements */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.heading-gradient {
  background: linear-gradient(135deg, var(--color-text-primary) 0%, var(--color-text-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
