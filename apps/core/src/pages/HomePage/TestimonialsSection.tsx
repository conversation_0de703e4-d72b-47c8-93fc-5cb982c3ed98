import React from 'react'

export default function TestimonialsSection() {
  return (
    <section className="py-20 bg-bg-default">
      <div className="container mx-auto px-6">
        <h2 className="text-3xl font-bold text-center mb-16 text-white">Success Stories</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          {/* Testimonial 1 */}
          <div className="card p-8 relative">
            <div className="absolute -top-5 -left-5 text-6xl text-secondary opacity-25">"</div>
            <div className="mb-6">
              <p className="text-gray-300 italic relative z-10">
                "AgriTech has allowed me to connect directly with farmers and invest in sustainable agriculture without having to own an entire farm. I love receiving updates about my plot's progress!"
              </p>
            </div>
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-primary to-primary-light text-white flex items-center justify-center mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div>
                <h4 className="font-semibold text-secondary">Sarah Johnson</h4>
                <p className="text-gray-400 text-sm">Plot Owner since 2022</p>
              </div>
            </div>
          </div>
          
          {/* Testimonial 2 */}
          <div className="card p-8 relative">
            <div className="absolute -top-5 -left-5 text-6xl text-secondary opacity-25">"</div>
            <div className="mb-6">
              <p className="text-gray-300 italic relative z-10">
                "I've always been interested in where my food comes from. With AgriTech, I can now participate in the growing process and have a direct connection to the source of my produce."
              </p>
            </div>
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-primary to-primary-light text-white flex items-center justify-center mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div>
                <h4 className="font-semibold text-secondary">Michael Chen</h4>
                <p className="text-gray-400 text-sm">Plot Owner since 2021</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 