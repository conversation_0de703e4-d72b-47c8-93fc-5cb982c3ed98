// Manipulate Strings:

// capitalize the first letter of a string
export const capitalizeString = (str) => {
  try {
    return str.charAt(0).toUpperCase() + str.slice(1);
  } catch (error) {
    return "";
  }
};

// Capitalize the first letter of each word in a string
export const capitalizeWords = (str) => {
  return str?.toLowerCase()?.replace(/\b\w/g, (match) => match?.toUpperCase());
};

// Truncate a string to a specified length and append ellipsis if necessary
export const truncateString = (str, maxLength) => {
  if (str.length <= maxLength) {
    return str;
  }
  return str.slice(0, maxLength) + "...";
};

// Remove whitespace from both ends of a string
export const removeWhitespace = (str) => str.trim();

// Convert a string to camelCase
export const convertToCamelCase = (str) => {
  const words = str?.split(" ");
  const camelCaseWords = words?.map((word, index) => {
    if (index === 0) {
      return word?.toLowerCase();
    }
    return word?.charAt(0)?.toUpperCase() + word?.slice(1)?.toLowerCase();
  });
  return camelCaseWords?.join("");
};

// Check if a string contains a specific substring
export const containsSubstring = (str, substring) => str.includes(substring);

// parseGPSString
export const parseGPSString = (gpsString) => {
  const [latitude, longitude] = gpsString.split(",").map(parseFloat);
  return { latitude, longitude };
};

export function toNormalCase(str: string): string {
  if (!str) return "-";
  
  if (typeof str !== 'string') return "-";
  
  let result = str
    .split("_")
    .map((word, index) => {
      if (!word || !word[0]) return ''; // Handle empty segments
      if (index === 0) {
        return word[0].toUpperCase() + word.slice(1).toLowerCase();
      }
      return word.toLowerCase();
    })
    .join(" ");

  return result
    .split("-")
    .map((word, index) => {
      if (!word || !word[0]) return ''; // Handle empty segments
      if (index === 0) {
        return word[0].toUpperCase() + word.slice(1).toLowerCase();
      }
      return word.toLowerCase();
    })
    .join(" ");
}

export function normalCaseToSnakeCase(str: string): string {
  return str
    .split(" ")
    .map((word, index) => {
      if (index === 0) {
        return word[0].toUpperCase() + word.slice(1).toLowerCase();
      }
      return word.toLowerCase();
    })
    .join("_");
}

export function toTitleCase(
  str: string | undefined,
  isNodeField?: boolean,
): string {
  if (!str) return "";
  let result = str
    .split("_")
    .map((word, index) => {
      if (isNodeField) return word;
      if (index === 0) {
        return checkUpperWords(
          word[0].toUpperCase() + word.slice(1).toLowerCase(),
        );
      }
      return checkUpperWords(word.toLowerCase());
    })
    .join(" ");

  return result
    .split("-")
    .map((word, index) => {
      if (isNodeField) return word;
      if (index === 0) {
        return checkUpperWords(
          word[0].toUpperCase() + word.slice(1).toLowerCase(),
        );
      }
      return checkUpperWords(word.toLowerCase());
    })
    .join(" ");
}

export const upperCaseWords: string[] = ["llm", "uri"];
export function checkUpperWords(str: string): string {
  const words = str.split(" ").map((word) => {
    return upperCaseWords.includes(word.toLowerCase())
      ? word.toUpperCase()
      : word[0].toUpperCase() + word.slice(1).toLowerCase();
  });

  return words.join(" ");
}