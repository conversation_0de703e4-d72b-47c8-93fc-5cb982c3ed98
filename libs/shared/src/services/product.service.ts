import { BaseService } from './base.service';
import {
  Product,
  ProductFilters,
  ProductSearchParams,
  ProductSearchResponse,
  PaginatedResponse,
  DropdownOption,
  CategoryDropdown,
  ApiResponse,
} from '../types';

export class ProductService extends BaseService {
  constructor(baseURL: string) {
    super(baseURL);
  }

  async getProducts(filters: ProductFilters = {}): Promise<PaginatedResponse<Product>> {
    const queryString = this.buildQueryString(filters);
    return this.handleRequest(
      this.api.get<ApiResponse<PaginatedResponse<Product>>>(`/api/products?${queryString}`)
    );
  }

  async getProductById(productId: string): Promise<Product> {
    return this.handleRequest(
      this.api.get<ApiResponse<Product>>(`/api/products/${productId}`)
    );
  }

  async searchProducts(params: ProductSearchParams): Promise<ProductSearchResponse> {
    const queryString = this.buildQueryString(params);
    return this.handleRequest(
      this.api.get<ApiResponse<ProductSearchResponse>>(`/api/search/products?${queryString}`)
    );
  }

  async getSimilarProducts(productId: string, limit = 10): Promise<Product[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<Product[]>>(`/api/search/products/${productId}/similar?limit=${limit}`)
    );
  }

  async getTrendingProducts(limit = 20): Promise<Product[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<Product[]>>(`/api/search/trending?limit=${limit}`)
    );
  }

  async incrementProductView(productId: string): Promise<void> {
    return this.handleRequest(
      this.api.post<ApiResponse<void>>(`/api/search/products/${productId}/view`)
    );
  }

  // Dropdown endpoints
  async getCategories(): Promise<CategoryDropdown[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<CategoryDropdown[]>>('/api/dropdowns/categories')
    );
  }

  async getStates(): Promise<DropdownOption[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<DropdownOption[]>>('/api/dropdowns/states')
    );
  }

  async getCitiesByState(stateId: string): Promise<DropdownOption[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<DropdownOption[]>>(`/api/dropdowns/states/${stateId}/cities`)
    );
  }

  async getProductTypes(): Promise<DropdownOption[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<DropdownOption[]>>('/api/dropdowns/product-types')
    );
  }

  async getUnits(): Promise<DropdownOption[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<DropdownOption[]>>('/api/dropdowns/units')
    );
  }

  async getGrades(): Promise<DropdownOption[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<DropdownOption[]>>('/api/dropdowns/grades')
    );
  }

  async getCertifications(): Promise<DropdownOption[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<DropdownOption[]>>('/api/dropdowns/certifications')
    );
  }

  async getFarmingMethods(): Promise<DropdownOption[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<DropdownOption[]>>('/api/dropdowns/farming-methods')
    );
  }

  async getHarvestSeasons(): Promise<DropdownOption[]> {
    return this.handleRequest(
      this.api.get<ApiResponse<DropdownOption[]>>('/api/dropdowns/harvest-seasons')
    );
  }

  async checkHealth(): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>('/health')
    );
  }

  async checkElasticsearchHealth(): Promise<any> {
    return this.handleRequest(
      this.api.get<ApiResponse<any>>('/api/search/health/elasticsearch')
    );
  }
}
