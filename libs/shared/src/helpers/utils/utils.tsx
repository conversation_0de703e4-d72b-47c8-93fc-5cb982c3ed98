import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, isValid, parse } from 'date-fns'
import axios from 'axios'
import { FileArchive, FileText } from "lucide-react";
import { Music } from "lucide-react";

export const SHORTCUT_KEYS = ["cmd", "ctrl", "mod", "alt", "shift"];

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const stringToBool = (str: string) => (str === "false" ? false : true);

export function sortShortcuts(a: string, b: string) {
  const order = SHORTCUT_KEYS;
  const aTrimmed = a.trim().toLowerCase();
  const bTrimmed = b.trim().toLowerCase();
  const aIndex = order.indexOf(aTrimmed);
  const bIndex = order.indexOf(bTrimmed);
  if (aIndex === -1 && bIndex === -1) {
    return aTrimmed.localeCompare(bTrimmed);
  }
  if (aIndex === -1) {
    return 1;
  }
  if (bIndex === -1) {
    return -1;
  }
  return aIndex - bIndex;
}
export function addPlusSignes(array: string[]): string[] {
  const exceptions = SHORTCUT_KEYS;
  // add + sign to the shortcuts beetwen characters that are not in the exceptions
  return array.map((key, index) => {
    if (index === 0) return key;
    if (
      exceptions.includes(key.trim().toLocaleLowerCase()) ||
      exceptions.includes(array[index - 1].trim().toLocaleLowerCase())
    )
      return key;

    return "+" + key;
  });
}



export function generateTextGradient(bgLinearGradient : string) {
  return {
    color: "transparent",
    background: bgLinearGradient,
    backgroundClip: "text",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
  };
}

export const getFileAsBlob = async (fileUrl: string) => {
  if (!fileUrl) return;
  try {
    const response = await axios.get(fileUrl, { responseType: "blob" });
    return new Blob([response.data]);
  } catch (error) {
      console.error(error);
  }
};

// Copy Text to Clipboard
export const copyToClipboard = async (text : string | any) => {
  try {
      await navigator.clipboard.writeText(text);
      return true;
  } catch {
      return false;
  }
};

// File Size Formatter
export const formatFileSize = (sizeInKB: number): string => {
  if (sizeInKB < 1024) {
    return `${sizeInKB.toFixed(2)} KB`;
  } else if (sizeInKB < 1024 * 1024) {
    const sizeInMB = sizeInKB / 1024;
    return `${sizeInMB.toFixed(2)} MB`;
  } else {
    const sizeInGB = sizeInKB / (1024 * 1024);
    return `${sizeInGB.toFixed(2)} GB`;
  }
}

// Get File Name
export const getFileName = (fileName : string) => fileName.split('/').pop();

// Get File Extension 
export const getFileExtension = (fileName : string) => fileName.split('.').pop();


// Capitalize the first letter of each word in a string
export const capitalizeWords = (str: string) => {
    return str?.toLowerCase()?.replace(/\b\w/g, (match: string) => match?.toUpperCase());
};

export function maskSensitiveData(data: string): string {
  if (/^[^@]+@[^@]+\.[^@]+$/.test(data)) {
    const [name, domain] = data.split('@');
    const maskedName = name.charAt(0) + "***" + name.charAt(name.length - 1);
    return `${maskedName}@${domain}`;
  } else if (/^\d{10}$/.test(data)) { 
    return "*".repeat(data.length - 2) + data.slice(-2); 
  } else {
    return data; 
  }
}

  export function formatPrice(price: number, showDecimals = false, currency = 'USD'): string {
    // Handle non-numeric input
    if (isNaN(price)) {
      return "";
    }
  
    if (currency === "") currency = "USD";
  
    // For very small amounts (less than 0.01), show all decimal places
    if (Math.abs(price) > 0 && Math.abs(price) < 0.01) {
      const formatter = new Intl.NumberFormat(currency === "USD" ? "en-US" : "en-IN", {
        style: "currency",
        currency,
        minimumFractionDigits: 6,
        maximumFractionDigits: 6
      });
      return formatter.format(price);
    }
  
    // Use Intl.NumberFormat for locale-specific formatting
    const formatter = new Intl.NumberFormat(currency === "USD" ? "en-US" : "en-IN", {
      style: "currency",
      currency,
      minimumFractionDigits: showDecimals ? 2 : 0,
    });
  
    return formatter.format(price);
  }


  export function formatCurrency(price: number, currency = 'USD'): string {
    // Handle non-numeric input
    if (isNaN(price)) {
      return "";
    }
  
    if (currency === "") currency = "USD";
  
    // Use Intl.NumberFormat for locale-specific formatting
    const formatter = new Intl.NumberFormat(currency === "USD" ? "en-US" : "en-IN", {
      style: "currency",
      currency,
      minimumFractionDigits: currency === 'INR' ? 2 : 6,
    });
  
    return formatter.format(price);
  }


export const valiDateAndFormat = (dateString: string, inputFormat: string = 'yyyy-MM-dd', outputFormat: string = 'LLL dd, y') =>
  isValid(parse(dateString, inputFormat, new Date())) && dateString === format(parse(dateString, inputFormat, new Date()), inputFormat)
    ? format(parse(dateString, inputFormat, new Date()), outputFormat)
    : null;

export function FormattedDate({ dateString }: { dateString: string }) {
  const date = new Date(dateString);
  return (
    <div>
      {isValid(date)
        ? format(date, 'LLL dd, y - hh:mm aa')
        : 'Invalid date'
      }
    </div>
  );
};


// Extract the number of days from the string (e.g., "P7D" -> 7)
export const formatDurationToDate = (durationStr: string) => {
  const match = durationStr && durationStr.match(/P(\d+)D/);
  if (!match) {
    return null;
  }

  const daysToAdd = parseInt(match[1], 10);
  const targetDate = new Date();
  targetDate.setDate(targetDate.getDate() + daysToAdd);

  return format(targetDate, "MMM dd, yyyy - hh:mm a");
}


export const getFileIcon = (fileType: string) => {
  if (fileType.startsWith('image/')) return Image;
  if (fileType.startsWith('audio/')) return Music;
  if (fileType.includes('pdf')) return FileText;
  if (fileType.includes('doc')) return FileText;
  if (fileType.includes('zip') || fileType.includes('rar')) return FileArchive;
  return File;
};


export const getFileStyles = (fileType: string) => {
  if (fileType.startsWith('image/')) {
    return {
      bg: 'bg-pink-100/50 dark:bg-pink-900/20',
      text: 'text-pink-500'
    };
  }
  if (fileType.startsWith('audio/')) {
    return {
      bg: 'bg-purple-100/50 dark:bg-purple-900/20',
      text: 'text-purple-500'
    };
  }
  if (fileType.includes('pdf')) {
    return {
      bg: 'bg-red-100/50 dark:bg-red-900/20',
      text: 'text-red-500'
    };
  }
  if (fileType.includes('doc')) {
    return {
      bg: 'bg-blue-100/50 dark:bg-blue-900/20',
      text: 'text-blue-500'
    };
  }
  if (fileType.includes('zip') || fileType.includes('rar')) {
    return {
      bg: 'bg-yellow-100/50 dark:bg-yellow-900/20',
      text: 'text-yellow-500'
    };
  }
  return {
    bg: 'bg-gray-100/50 dark:bg-gray-900/20',
    text: 'text-gray-500'
  };
};

// These 2 functions used for the CustomInitScreen of the app
export const isEmpty = (listLength: number, query: string | null, isFiltering: () => boolean) => {
  const isCurrentlyEmpty = listLength === 0;
  const isSearching = query !== null && query !== '';
  const isFilteringActive = isFiltering();

  console.log(`isCurrentlyEmpty: ${isCurrentlyEmpty}\nisSearching: ${isSearching}\nisFilteringActive: ${isFilteringActive}`);
  
  if (!isSearching && !isFilteringActive) {
    return false;
  }
  return isCurrentlyEmpty && (isSearching || isFilteringActive);
};

export const getButtonDisabled = (listLength: number, query: string | null) => {
  if (listLength === 0 && (query?.length ?? 0) === 0) {
    return false;
  }
  return true;
};