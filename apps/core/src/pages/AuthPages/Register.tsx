import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'

export default function Register() {
  const [registerMethod, setRegisterMethod] = useState<'email' | 'phone'>('email');
  const [step, setStep] = useState(1);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const navigate = useNavigate();

  const handleNext = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (registerMethod === 'email') {
      if (!firstName || !lastName || !email) {
        setError('Please fill in all fields');
        return;
      }
    } else {
      if (!firstName || !lastName || !phoneNumber) {
        setError('Please fill in all fields');
        return;
      }
    }
    
    // Move to step 2 (password creation for email or OTP for phone)
    setStep(2);
    setError('');
  };

  const handleSendOtp = async () => {
    if (!phoneNumber) {
      setError('Please enter a valid phone number');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      // Simulated API call to send OTP
      // Replace with actual API call
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to send OTP');
      }
      
    } catch (err: any) {
      setError(err.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteRegistration = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (registerMethod === 'email') {
      if (!password || !confirmPassword) {
        setError('Please create a password');
        return;
      }
      
      if (password !== confirmPassword) {
        setError('Passwords do not match');
        return;
      }
    } else {
      if (!otp) {
        setError('Please enter the OTP');
        return;
      }
    }

    try {
      setLoading(true);
      setError('');
      
      // Create the registration data based on the method
      const registrationData = registerMethod === 'email' 
        ? { firstName, lastName, email, password }
        : { firstName, lastName, phoneNumber, otp };
      
      // Simulated API call for registration
      // Replace with actual API call
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }
      
      // Store token if returned
      if (data.token) {
        localStorage.setItem('authToken', data.token);
      }
      
      // Redirect to dashboard or login
      navigate('/auth/login');
    } catch (err: any) {
      setError(err.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-bg-default">
      <div className="bg-bg-paper p-8 rounded-xl shadow-xl w-full max-w-md border border-white/10">
        <div className="flex justify-center mb-4">
          <h1 className="text-3xl font-bold text-white flex items-center">
            <span className="text-secondary mr-1 font-extrabold">Agri</span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-secondary to-secondary-light">Tech</span>
          </h1>
        </div>
        <h2 className="text-xl font-medium mb-6 text-center text-white">Create Your Account</h2>
        
        {/* Registration Method Selector */}
        <div className="bg-white/5 rounded-lg p-1 flex mb-6">
          <button
            onClick={() => setRegisterMethod('email')}
            className={`flex-1 py-2 rounded-md text-sm font-medium transition-all ${
              registerMethod === 'email' 
                ? 'bg-white/10 text-white' 
                : 'text-gray-400 hover:text-white'
            }`}
            disabled={step > 1}
          >
            Register with Email
          </button>
          <button
            onClick={() => setRegisterMethod('phone')}
            className={`flex-1 py-2 rounded-md text-sm font-medium transition-all ${
              registerMethod === 'phone' 
                ? 'bg-white/10 text-white' 
                : 'text-gray-400 hover:text-white'
            }`}
            disabled={step > 1}
          >
            Register with Phone
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-500 text-sm">
            {error}
          </div>
        )}
        
        {step === 1 ? (
          <form className="space-y-4" onSubmit={handleNext}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-2 text-sm">First Name</label>
                <input 
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                  placeholder="John"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-300 mb-2 text-sm">Last Name</label>
                <input 
                  type="text"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                  placeholder="Doe"
                  required
                />
              </div>
            </div>
            
            {registerMethod === 'email' ? (
              <div>
                <label className="block text-gray-300 mb-2 text-sm">Email Address</label>
                <input 
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            ) : (
              <div>
                <label className="block text-gray-300 mb-2 text-sm">Phone Number</label>
                <input 
                  type="tel"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                  placeholder="+1234567890"
                  required
                />
              </div>
            )}
            
            <button 
              type="submit"
              className="w-full btn-primary py-2 rounded-lg font-medium transition-all"
            >
              Continue
            </button>
          </form>
        ) : (
          <form className="space-y-4" onSubmit={handleCompleteRegistration}>
            {registerMethod === 'email' ? (
              <>
                <div>
                  <label className="block text-gray-300 mb-2 text-sm">Create Password</label>
                  <input 
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                    placeholder="********"
                    required
                  />
                  <div className="mt-1 text-xs text-gray-500">
                    Password must be at least 8 characters
                  </div>
                </div>
                <div>
                  <label className="block text-gray-300 mb-2 text-sm">Confirm Password</label>
                  <input 
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                    placeholder="********"
                    required
                  />
                </div>
              </>
            ) : (
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-gray-300 text-sm">Verification Code</label>
                  <button 
                    type="button" 
                    onClick={handleSendOtp}
                    className="text-secondary text-xs hover:underline"
                    disabled={loading}
                  >
                    {loading ? 'Sending...' : 'Send Code'}
                  </button>
                </div>
                <input 
                  type="text"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50 text-center tracking-widest" 
                  placeholder="● ● ● ● ● ●"
                  maxLength={6}
                  required
                />
                <div className="mt-1 text-xs text-gray-500">
                  We've sent a 6-digit code to {phoneNumber}
                </div>
              </div>
            )}
            
            <div className="flex space-x-3 pt-2">
              <button 
                type="button"
                onClick={() => setStep(1)}
                className="flex-1 py-2 rounded-lg font-medium border border-gray-500 text-gray-300 hover:bg-white/5 transition-all"
              >
                Back
              </button>
              <button 
                type="submit"
                className="flex-1 btn-primary py-2 rounded-lg font-medium transition-all flex justify-center items-center"
                disabled={loading}
              >
                {loading ? (
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  'Create Account'
                )}
              </button>
            </div>
          </form>
        )}
        
        <div className="mt-8 text-center">
          <p className="text-gray-400">Already have an account? <Link to="/auth/login" className="text-secondary hover:underline">Login</Link></p>
        </div>
        
        <div className="relative flex items-center mt-8">
          <div className="flex-grow border-t border-gray-700"></div>
          <span className="flex-shrink mx-4 text-gray-500 text-sm">Or continue with</span>
          <div className="flex-grow border-t border-gray-700"></div>
        </div>
        
        <div className="flex space-x-4 mt-6">
          <button className="flex-1 flex items-center justify-center py-2 px-4 border border-white/10 rounded-lg text-white bg-white/5 hover:bg-white/10 transition-all">
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12.545 10.239v3.821h5.445c-.712 2.315-2.647 3.972-5.445 3.972-3.332 0-6.033-2.701-6.033-6.032s2.701-6.032 6.033-6.032c1.498 0 2.866.549 3.921 1.453l2.814-2.814C17.503 2.988 15.139 2 12.545 2 7.021 2 2.543 6.477 2.543 12s4.478 10 10.002 10c8.396 0 10.249-7.85 9.426-11.748l-9.426-.013z"/>
            </svg>
            Google
          </button>
          <button className="flex-1 flex items-center justify-center py-2 px-4 border border-white/10 rounded-lg text-white bg-white/5 hover:bg-white/10 transition-all">
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
            Facebook
          </button>
        </div>
      </div>
    </div>
  )
}
